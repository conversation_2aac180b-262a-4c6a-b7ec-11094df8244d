#!/usr/bin/env python3
"""
测试移动端登录界面流程
"""

import sys
import os
import logging

# 添加移动端路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')

def test_login_screen_logic():
    """测试登录界面逻辑"""
    
    print("=" * 50)
    print("测试移动端登录界面逻辑")
    print("=" * 50)
    
    try:
        # 模拟登录界面的认证逻辑
        from utils.cloud_api import get_cloud_api
        from utils.security_config import login_limiter
        
        # 测试参数
        username = "markey"
        password = "markey0308@163"
        identity = "超级管理员"
        
        print(f"测试用户: {username}")
        print(f"测试密码: {password}")
        print(f"选择身份: {identity}")
        print()
        
        # 1. 检查身份选择
        if not identity or identity == "请选择登录身份":
            print("✗ 请先选择登录身份")
            return False
        print("✓ 身份选择检查通过")
        
        # 2. 检查输入
        if not username or not password:
            print("✗ 请输入用户名和密码")
            return False
        print("✓ 输入检查通过")
        
        # 3. 检查登录限制
        if not login_limiter.is_allowed(username):
            print("✗ 登录尝试过于频繁，请稍后再试")
            return False
        print("✓ 登录限制检查通过")
        
        # 4. 执行云端认证
        cloud_api = get_cloud_api()
        if cloud_api:
            print("✓ 云端API可用，开始认证...")
            
            # 设置超时
            original_timeout = getattr(cloud_api, 'timeout', 30)
            cloud_api.timeout = 10
            
            try:
                auth_result = cloud_api.authenticate(username, password)
                if auth_result and auth_result.get('status') == 'success':
                    print("✓ 云端认证成功")
                    login_limiter.record_attempt(username, True)
                    
                    # 模拟设置用户会话
                    print("✓ 开始设置用户会话...")
                    
                    # 检查认证结果
                    user_id = auth_result.get("user_id")
                    custom_id = auth_result.get("custom_id", user_id)
                    user_info = auth_result.get('user_info', {})
                    
                    print(f"  用户ID: {user_id}")
                    print(f"  自定义ID: {custom_id}")
                    print(f"  用户信息: {user_info}")
                    
                    # 构建用户数据
                    user_data = {
                        'custom_id': custom_id,
                        'user_id': user_id,
                        'username': username,
                        'full_name': user_info.get('full_name', username),
                        'role': user_info.get('role', identity),
                        'auth_type': 'cloud'
                    }
                    
                    print(f"✓ 用户数据构建完成: {user_data}")
                    print("✓ 登录流程完成")
                    return True
                else:
                    print(f"✗ 云端认证失败: {auth_result}")
                    return False
            finally:
                cloud_api.timeout = original_timeout
        else:
            print("✗ 云端API不可用")
            return False
            
    except Exception as e:
        print(f"✗ 登录流程异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试移动端登录界面逻辑...")
    
    success = test_login_screen_logic()
    
    print("\n" + "=" * 50)
    print("测试结果")
    print("=" * 50)
    
    if success:
        print("✓ 登录界面逻辑测试成功")
        print("\n建议:")
        print("1. 检查移动端应用的UI响应")
        print("2. 检查用户会话管理")
        print("3. 检查页面导航逻辑")
    else:
        print("✗ 登录界面逻辑测试失败")
        print("\n建议:")
        print("1. 检查网络连接")
        print("2. 检查后端服务状态")
        print("3. 检查用户数据")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试移动端密码验证
"""

import sys
import os

# 添加移动端路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_mobile_password_verification():
    """测试移动端密码验证"""
    
    print("测试移动端密码验证...")
    
    # 检查passlib是否可用
    try:
        from passlib.context import CryptContext
        print("✓ passlib模块可用")
        
        pwd_context = CryptContext(schemes=["sha256_crypt", "bcrypt"], deprecated="auto")
        
        # 测试密码验证
        stored_hash = "$5$rounds=535000$dGnV9R0y4at7E3nF$WOdH8NuTibm61p3TKs3MNqyZAQPamKfLWZWzy0hZz/3"
        test_password = "markey0308@163"
        
        result = pwd_context.verify(test_password, stored_hash)
        print(f"密码验证结果: {'✓ 成功' if result else '✗ 失败'}")
        
    except ImportError as e:
        print(f"✗ passlib模块不可用: {e}")
        
    # 测试移动端的密码验证函数
    try:
        from utils.password_utils import verify_password
        print("✓ 移动端密码验证函数可用")
        
        stored_hash = "$5$rounds=535000$dGnV9R0y4at7E3nF$WOdH8NuTibm61p3TKs3MNqyZAQPamKfLWZWzy0hZz/3"
        test_password = "markey0308@163"
        
        result = verify_password(test_password, stored_hash)
        print(f"移动端密码验证结果: {'✓ 成功' if result else '✗ 失败'}")
        
    except ImportError as e:
        print(f"✗ 移动端密码验证函数不可用: {e}")
    except Exception as e:
        print(f"✗ 移动端密码验证出错: {e}")

if __name__ == "__main__":
    test_mobile_password_verification()

import sqlite3
import os

def check_user_password():
    """检查测试用户的密码哈希"""
    try:
        # 连接到数据库
        db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app.db')
        print(f"数据库路径: {db_path}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查markey用户密码哈希
        cursor.execute("SELECT username, hashed_password FROM users WHERE username = ?", ('markey',))
        user = cursor.fetchone()
        if user:
            print(f'用户名: {user[0]}')
            print(f'密码哈希: {user[1]}')
        else:
            print("markey用户不存在")
            
        # 检查所有用户
        cursor.execute("SELECT username, hashed_password FROM users")
        all_users = cursor.fetchall()
        print("\n所有用户:")
        for u in all_users:
            print(f'用户名: {u[0]}, 密码哈希: {u[1][:50]}...')
        
        conn.close()
        
    except Exception as e:
        print(f"操作时出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_user_password()
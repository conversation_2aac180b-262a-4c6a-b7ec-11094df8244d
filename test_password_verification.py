#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试密码验证脚本
用于验证markey用户的密码哈希是否正确
"""

from passlib.context import CryptContext
import hashlib
import base64

# 设置密码上下文
pwd_context = CryptContext(schemes=["sha256_crypt", "bcrypt"], deprecated="auto")

def simple_hash(password: str) -> str:
    """简单哈希函数"""
    salt = "health_app_salt_2024"
    combined = salt + password
    hash_obj = hashlib.sha256(combined.encode('utf-8'))
    return base64.b64encode(hash_obj.digest()).decode('utf-8')

def test_password_verification():
    """测试密码验证"""
    # 从后端数据库获取的markey用户密码哈希
    stored_hash = "$5$rounds=535000$dGnV9R0y4at7E3nF$WOdH8NuTibm61p3TKs3MNqyZAQPamKfLWZWzy0hZz/3"
    
    # 测试不同的密码
    test_passwords = [
        "markey0308@163",  # 常用密码
        "markey",          # 用户名
        "123456",          # 简单密码
        "password",        # 默认密码
        "admin",           # 管理员密码
        "test123"          # 测试密码
    ]
    
    print(f"存储的密码哈希: {stored_hash}")
    print("=" * 60)
    
    for password in test_passwords:
        print(f"\n测试密码: '{password}'")
        
        # 1. 使用passlib验证
        try:
            result1 = pwd_context.verify(password, stored_hash)
            print(f"  passlib验证结果: {result1}")
        except Exception as e:
            print(f"  passlib验证错误: {e}")
        
        # 2. 使用简单哈希验证
        try:
            simple_hashed = simple_hash(password)
            result2 = (simple_hashed == stored_hash)
            print(f"  简单哈希验证结果: {result2}")
            if result2:
                print(f"  简单哈希值: {simple_hashed}")
        except Exception as e:
            print(f"  简单哈希验证错误: {e}")
    
    # 测试生成新的哈希
    print("\n" + "=" * 60)
    print("生成新的密码哈希:")
    test_password = "markey0308@163"
    
    try:
        new_hash = pwd_context.hash(test_password)
        print(f"新生成的哈希: {new_hash}")
        
        # 验证新生成的哈希
        verify_result = pwd_context.verify(test_password, new_hash)
        print(f"新哈希验证结果: {verify_result}")
    except Exception as e:
        print(f"生成新哈希错误: {e}")

if __name__ == "__main__":
    test_password_verification()
2025-09-20 08:39:11 [INFO] [logging_config] [configure_logging:172] - 日志系统配置完成 - 级别: INFO, 文件: backend_20250920.log
2025-09-20 08:39:11 [INFO] [logging_config] [configure_logging:173] - SQLAlchemy日志级别: WARNING
2025-09-20 08:39:11 [INFO] [app.db.base_session] [<module>:45] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-09-20 08:39:11 [INFO] [app.core.db_connection] [_create_engine:192] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-09-20 08:39:11 [INFO] [auth_service] [__init__:49] - 统一认证服务初始化完成
2025-09-20 08:39:12 [INFO] [backend.app.core.db_connection] [_create_engine:192] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-09-20 08:39:12 [INFO] [query_cache] [__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-09-20 08:39:12 [INFO] [BackendMockDataManager] [__init__:35] - 后端模拟数据模式已启用
2025-09-20 08:39:13 [INFO] [root] [<module>:28] - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-09-20 08:39:13 [INFO] [fallback_manager] [register_dependency:51] - 依赖 psutil (psutil) 可用
2025-09-20 08:39:13 [INFO] [health_monitor] [__init__:115] - 健康监控器初始化完成
2025-09-20 08:39:13 [INFO] [app.core.system_monitor] [_load_history:254] - 已加载 288 个历史数据点
2025-09-20 08:39:13 [INFO] [app.core.alert_detector] [_load_rules:464] - 已加载 6 个告警规则
2025-09-20 08:39:13 [INFO] [app.core.alert_detector] [_load_alerts:484] - 已加载 0 个当前告警
2025-09-20 08:39:13 [INFO] [app.core.alert_detector] [_load_alerts:491] - 已加载 370 个历史告警
2025-09-20 08:39:13 [INFO] [app.core.alert_detector] [_load_notification_channels:502] - 已加载 1 个通知渠道
2025-09-20 08:39:13 [INFO] [query_cache] [__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-09-20 08:39:13 [INFO] [alert_manager] [_init_alert_rules:315] - 已初始化默认告警规则
2025-09-20 08:39:13 [INFO] [alert_manager] [_init_notification_channels:333] - 已初始化默认通知渠道
2025-09-20 08:39:13 [INFO] [alert_manager] [__init__:258] - 告警管理器初始化完成
2025-09-20 08:39:14 [INFO] [db_service] [_create_engine:92] - 数据库引擎和会话工厂创建成功
2025-09-20 08:39:14 [INFO] [db_service] [__init__:56] - 数据库服务初始化完成
2025-09-20 08:39:14 [INFO] [notification_service] [__init__:55] - 通知服务初始化完成
2025-09-20 08:39:14 [INFO] [main] [<module>:56] - 错误处理模块导入成功
2025-09-20 08:39:14 [INFO] [main] [<module>:65] - 监控模块导入成功
2025-09-20 08:39:14 [INFO] [main] [<module>:75] - 不再使用迁移脚本，使用标准化的数据库模型
2025-09-20 08:39:14 [INFO] [error_handling] [setup_error_handling:271] - 错误处理已设置
2025-09-20 08:39:14 [INFO] [main] [<module>:88] - 错误处理系统已设置
2025-09-20 08:39:15 [INFO] [main] [startup_event:399] - 应用启动中...
2025-09-20 08:39:15 [INFO] [main] [startup_event:402] - 错误处理系统已在应用创建时初始化
2025-09-20 08:39:15 [INFO] [monitoring] [init_monitoring:441] - 添加指标端点成功: /metrics
2025-09-20 08:39:15 [INFO] [monitoring] [init_monitoring:448] - 添加健康检查端点成功: /health
2025-09-20 08:39:15 [INFO] [monitoring] [init_monitoring:454] - 添加详细健康检查端点成功: /api/health/detailed
2025-09-20 08:39:15 [INFO] [monitoring] [init_system_info:103] - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-09-20 08:39:15 [INFO] [monitoring] [init_monitoring:464] - 启动资源监控线程成功
2025-09-20 08:39:15 [INFO] [monitoring] [init_monitoring:466] - 监控系统初始化成功（不使用中间件）
2025-09-20 08:39:15 [INFO] [monitoring] [init_monitoring:470] - 监控系统初始化完成
2025-09-20 08:39:15 [INFO] [main] [startup_event:409] - 监控系统初始化完成
2025-09-20 08:39:15 [INFO] [app.db.init_db] [<module>:45] - 所有模型导入成功
2025-09-20 08:39:15 [INFO] [app.db.init_db] [<module>:53] - 使用sha256_crypt进行密码哈希和验证
2025-09-20 08:39:15 [INFO] [db_service] [get_session:114] - 数据库连接成功 (尝试 1/3)
2025-09-20 08:39:15 [INFO] [app.db.init_db] [init_db:67] - 所有模型导入成功
2025-09-20 08:39:15 [INFO] [app.db.init_db] [init_db:71] - 使用sha256_crypt进行密码哈希和验证
2025-09-20 08:39:15 [INFO] [app.db.init_db] [init_db:74] - 正在运行数据库迁移...
2025-09-20 08:39:15 [INFO] [app.db.init_db] [init_db:93] - 正在检查并更新数据库表结构...
2025-09-20 08:39:15 [INFO] [app.db.init_db] [init_db:97] - 数据库表结构检查和更新完成
2025-09-20 08:39:15 [INFO] [app.db.init_db] [_setup_model_relationships:120] - 模型关系初始化完成
2025-09-20 08:39:15 [INFO] [app.db.init_db] [init_db:101] - 模型关系设置完成
2025-09-20 08:39:15 [INFO] [main] [startup_event:418] - 数据库初始化完成（强制重建）
2025-09-20 08:39:15 [INFO] [db_service] [get_session:114] - 数据库连接成功 (尝试 1/3)
2025-09-20 08:39:15 [INFO] [main] [startup_event:425] - 数据库连接正常
2025-09-20 08:39:15 [INFO] [main] [startup_event:433] - 开始初始化模板数据
2025-09-20 08:39:15 [INFO] [db_service] [get_session:114] - 数据库连接成功 (尝试 1/3)
2025-09-20 08:39:16 [INFO] [app.db.init_templates] [init_assessment_templates:33] - 开始初始化评估量表模板...
2025-09-20 08:39:16 [INFO] [app.db.init_templates] [init_assessment_templates:72] - 成功初始化 5 个评估量表模板
2025-09-20 08:39:16 [INFO] [app.db.init_templates] [init_questionnaire_templates:85] - 开始初始化调查问卷模板...
2025-09-20 08:39:16 [INFO] [app.db.init_templates] [init_questionnaire_templates:116] - 成功初始化 5 个调查问卷模板
2025-09-20 08:39:16 [INFO] [main] [startup_event:437] - 模板数据初始化完成
2025-09-20 08:39:16 [INFO] [main] [startup_event:442] - 数据库表结构已经标准化，不需要迁移
2025-09-20 08:39:16 [INFO] [main] [startup_event:445] - 应用启动完成
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:39:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 08:39:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:39:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 08:39:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:39:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 08:39:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 08:39:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:39:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:39:29 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 08:39:29 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:39:29 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 08:39:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 08:39:29 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:39:29 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:39:33 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 08:39:33 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:39:33 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:39:33 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:39:33 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 08:39:33 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:39:33 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:39:33 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 08:39:33 [INFO] [app.core.db_connection] [connect:176] - 数据库连接已创建
2025-09-20 08:39:33 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 08:39:33 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 08:39:33 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:39:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 08:39:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:39:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:39:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:39:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 08:39:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:39:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:39:58 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 08:39:58 [INFO] [app.core.db_connection] [connect:176] - 数据库连接已创建
2025-09-20 08:39:58 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 08:39:58 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 08:39:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:41:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 08:41:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:41:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:41:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:41:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 08:41:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:41:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:41:16 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 08:41:16 [INFO] [app.core.db_connection] [connect:176] - 数据库连接已创建
2025-09-20 08:41:16 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 08:41:16 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 08:41:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:44:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 08:44:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:44:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 08:44:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:44:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 08:44:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 08:44:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:44:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:44:32 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 08:44:32 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:44:32 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 08:44:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 08:44:32 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:44:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:45:13 [WARNING] [alert_manager] [_create_alert:649] - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-09-20 08:46:13 [WARNING] [alert_manager] [_create_alert:649] - 触发告警: disk_usage, 当前值: 92.0, 阈值: 90
2025-09-20 08:53:30 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:53:30 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:53:30 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:53:30 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 08:53:30 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 08:53:30 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:53:30 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:53:30 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:53:30 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:53:30 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:53:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 08:53:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:53:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 08:53:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:53:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 08:53:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 08:53:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:53:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:53:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:53:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:53:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:53:32 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 08:53:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 08:53:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 08:53:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 08:53:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 08:53:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 08:53:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 08:53:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 08:53:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 08:53:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 08:53:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 08:53:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 08:53:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 08:53:32 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 08:53:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:53:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:53:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:53:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:53:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:53:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:53:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:53:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:53:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:53:45 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 08:53:45 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:53:45 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:53:45 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:53:45 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 08:53:45 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:53:45 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:53:45 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 08:53:45 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 08:53:45 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 08:53:46 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 08:53:46 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 08:53:46 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:54:50 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 08:54:50 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:54:50 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 08:54:50 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:54:50 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 08:54:50 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:54:50 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 08:54:50 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 08:54:50 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:54:50 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:54:51 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:54:51 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:54:51 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:54:51 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:54:51 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 08:54:51 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 08:54:51 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 08:54:51 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 08:54:51 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 08:54:51 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 08:54:51 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 08:54:51 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 08:54:51 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 08:54:51 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 08:54:51 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 08:54:51 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 08:54:51 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 08:54:51 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 08:54:51 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:54:51 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:54:51 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:54:51 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:54:51 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:54:51 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:54:51 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:54:51 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:54:51 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:55:10 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 08:55:10 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:55:10 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:55:10 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:55:10 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 08:55:10 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:55:10 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:55:10 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 08:55:10 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 08:55:10 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 08:55:10 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 08:55:10 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 08:55:10 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:57:22 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 08:57:22 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:57:22 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 08:57:22 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:57:22 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:57:22 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 08:57:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 08:57:22 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:57:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 08:57:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 08:57:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 08:57:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 08:57:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 08:57:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 08:57:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 08:57:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 08:57:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 08:57:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 08:57:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 08:57:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:57:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:57:24 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:57:24 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:57:24 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:57:24 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 08:57:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 08:57:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 08:57:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 08:57:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 08:57:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 08:57:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 08:57:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 08:57:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 08:57:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 08:57:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 08:57:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 08:57:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 08:57:24 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 08:57:24 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:57:24 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:57:24 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:57:24 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:57:24 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:57:24 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:57:24 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:57:24 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:57:24 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:57:42 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 08:57:42 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:57:42 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:57:42 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:57:42 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 08:57:42 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:57:42 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:57:42 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 08:57:42 [INFO] [app.core.db_connection] [connect:176] - 数据库连接已创建
2025-09-20 08:57:42 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 08:57:42 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 08:57:43 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 08:57:43 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 08:57:43 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:58:02 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 08:58:02 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:58:02 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:58:02 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:58:02 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 08:58:02 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:58:02 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:58:02 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 08:58:02 [INFO] [app.core.db_connection] [connect:176] - 数据库连接已创建
2025-09-20 08:58:02 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 08:58:02 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 08:58:03 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 08:58:03 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 08:58:03 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:59:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 08:59:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:59:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 08:59:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:59:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 08:59:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 08:59:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:59:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:59:32 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 08:59:32 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:59:32 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 08:59:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 08:59:32 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 08:59:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:59:33 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:59:33 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:59:46 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 08:59:46 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:59:46 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:59:46 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 08:59:46 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 08:59:46 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 08:59:46 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 08:59:46 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 08:59:46 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 08:59:46 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 08:59:46 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 08:59:46 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 08:59:46 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:00:04 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:00:04 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:00:04 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:00:04 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:00:04 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:00:04 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:00:04 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:00:04 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:00:04 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:00:04 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:00:05 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:00:05 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:00:05 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:07:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 09:07:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:07:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 09:07:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:07:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 09:07:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 09:07:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:07:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:07:29 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 09:07:29 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:07:29 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 09:07:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 09:07:29 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:07:29 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:07:30 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:07:30 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:07:30 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:07:30 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:07:50 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:07:50 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:07:50 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:07:50 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:07:50 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:07:50 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:07:50 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:07:50 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:07:50 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:07:50 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:07:51 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:07:51 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:07:51 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:08:35 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:08:35 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:08:35 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:08:35 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:08:35 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:08:35 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:08:35 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:08:35 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:08:35 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:08:35 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:08:35 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:08:35 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:08:35 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:10:21 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:10:21 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:10:21 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:10:21 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:10:21 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:10:21 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:10:21 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:10:21 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:10:21 [INFO] [app.core.db_connection] [connect:176] - 数据库连接已创建
2025-09-20 09:10:21 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:10:21 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:10:22 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:10:22 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:10:22 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:13:34 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:13:35 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 09:13:35 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:13:35 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 09:13:35 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:13:35 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 09:13:35 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:13:35 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 09:13:35 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 09:13:35 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:13:35 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:13:36 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:13:36 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:13:36 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:13:36 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:13:36 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:13:36 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 09:13:36 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 09:13:36 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 09:13:36 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 09:13:36 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 09:13:36 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 09:13:36 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 09:13:36 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 09:13:36 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 09:13:36 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 09:13:36 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 09:13:36 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 09:13:37 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 09:13:37 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 09:13:37 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:13:37 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:13:37 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:13:37 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:13:37 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:13:37 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:13:37 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:13:37 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:13:37 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:13:51 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:13:51 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:13:51 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:13:51 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:13:51 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:13:51 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:13:51 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:13:51 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:13:51 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:13:51 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:13:52 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:13:52 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:13:52 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:14:17 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:14:17 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:14:17 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:14:17 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:14:17 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:14:17 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:14:17 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:14:17 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:14:17 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:14:17 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:14:18 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:14:18 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:14:18 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:15:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 09:15:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:15:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 09:15:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 09:15:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 09:15:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 09:15:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 09:15:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:15:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:15:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 09:15:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 09:15:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 09:15:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 09:15:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 09:15:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 09:15:16 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:15:16 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 09:15:16 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:15:16 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 09:15:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 09:15:16 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:15:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:15:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:15:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:15:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:15:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:15:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:15:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:15:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:15:32 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:15:32 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:15:32 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:15:33 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:15:33 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:15:33 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:15:51 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:15:51 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:15:51 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:15:51 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:15:51 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:15:51 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:15:51 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:15:51 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:15:51 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:15:51 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:15:51 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:15:51 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:15:51 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:22:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 09:22:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:22:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 09:22:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:22:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 09:22:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:22:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 09:22:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 09:22:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:22:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:22:24 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:22:24 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:22:24 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:22:24 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:22:24 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 09:22:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 09:22:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 09:22:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 09:22:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 09:22:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 09:22:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 09:22:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 09:22:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 09:22:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 09:22:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 09:22:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 09:22:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 09:22:24 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 09:22:24 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:22:24 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:22:24 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:22:24 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:22:24 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:22:25 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:22:25 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:22:25 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:22:25 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:22:40 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:22:40 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:22:40 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:22:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:22:40 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:22:40 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:22:40 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:22:40 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:22:40 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:22:40 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:22:41 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:22:41 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:22:41 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:23:01 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:23:01 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:23:01 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:23:01 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:23:01 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:23:01 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:23:01 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:23:01 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:23:01 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:23:01 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:23:02 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:23:02 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:23:02 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:26:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:26:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:26:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:26:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 09:26:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 09:26:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:26:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:26:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:26:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:26:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:26:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:26:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:26:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:26:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 09:26:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 09:26:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 09:26:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 09:26:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 09:26:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 09:26:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:26:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:26:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:26:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:26:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:26:24 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 09:26:24 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:26:24 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 09:26:24 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 09:26:24 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:26:24 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:26:25 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:26:25 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:26:25 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:26:25 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 09:26:25 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 09:26:25 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 09:26:25 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 09:26:25 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 09:26:25 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 09:26:25 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 09:26:25 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 09:26:25 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 09:26:25 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 09:26:25 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 09:26:25 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 09:26:25 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 09:26:25 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 09:26:25 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:26:25 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:26:25 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:26:25 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:26:25 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:26:25 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:26:25 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:26:25 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:26:25 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:26:39 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:26:39 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:26:39 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:26:39 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:26:39 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:26:39 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:26:39 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:26:39 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:26:39 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:26:39 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:26:40 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:26:40 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:26:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:31:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 09:31:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:31:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 09:31:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:31:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 09:31:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 09:31:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:31:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:31:16 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 09:31:16 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:31:16 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 09:31:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 09:31:16 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:31:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:31:37 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:31:37 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:31:37 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:31:37 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:31:37 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:31:37 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:31:37 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:31:37 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:31:37 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:31:37 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:31:37 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:31:37 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:31:37 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:09 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 09:33:09 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:09 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 09:33:09 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:09 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 09:33:09 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 09:33:09 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:09 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:10 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 09:33:10 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:10 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 09:33:10 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 09:33:10 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:10 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:11 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:33:11 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:11 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:11 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:33:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:33:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:23 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:33:23 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:33:23 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:33:23 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:33:23 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:33:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:57 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:33:57 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:57 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:58 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 09:33:58 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:58 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 09:33:58 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:58 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 09:33:58 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:58 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 09:33:58 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:58 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 09:33:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 09:33:58 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:33:59 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:33:59 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:33:59 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:33:59 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:34:19 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:34:19 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:34:19 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:34:19 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:34:19 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:34:19 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:34:19 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:34:19 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:34:19 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:34:19 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:34:19 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:34:19 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:34:19 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 09:35:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 09:35:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 09:35:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 09:35:15 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 09:35:15 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:15 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:35:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:16 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 09:35:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 09:35:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 09:35:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 09:35:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 09:35:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 09:35:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 09:35:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 09:35:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 09:35:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 09:35:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 09:35:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 09:35:16 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 09:35:16 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 09:35:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:35:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:16 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:35:16 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:16 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:16 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:35:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:35:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:32 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:35:32 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:35:32 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:35:33 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:35:33 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:35:33 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:56 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 09:35:56 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:56 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 09:35:56 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:56 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 09:35:56 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:56 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 09:35:56 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:56 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 09:35:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 09:35:56 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:35:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:36:01 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:36:01 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:36:01 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:36:01 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:36:01 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:36:01 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:36:01 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:36:01 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:36:01 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:36:01 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:36:02 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:36:02 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:36:02 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:41:21 [INFO] [logging_config] [configure_logging:172] - 日志系统配置完成 - 级别: INFO, 文件: backend_20250920.log
2025-09-20 09:41:21 [INFO] [logging_config] [configure_logging:173] - SQLAlchemy日志级别: WARNING
2025-09-20 09:41:21 [INFO] [app.db.base_session] [<module>:45] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-09-20 09:41:22 [INFO] [app.core.db_connection] [_create_engine:192] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-09-20 09:41:22 [INFO] [auth_service] [__init__:49] - 统一认证服务初始化完成
2025-09-20 09:41:22 [INFO] [backend.app.core.db_connection] [_create_engine:192] - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-09-20 09:41:22 [INFO] [query_cache] [__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-09-20 09:41:23 [INFO] [BackendMockDataManager] [__init__:35] - 后端模拟数据模式已启用
2025-09-20 09:41:24 [INFO] [root] [<module>:28] - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-09-20 09:41:24 [INFO] [fallback_manager] [register_dependency:51] - 依赖 psutil (psutil) 可用
2025-09-20 09:41:24 [INFO] [health_monitor] [__init__:115] - 健康监控器初始化完成
2025-09-20 09:41:24 [INFO] [app.core.system_monitor] [_load_history:254] - 已加载 288 个历史数据点
2025-09-20 09:41:24 [INFO] [app.core.alert_detector] [_load_rules:464] - 已加载 6 个告警规则
2025-09-20 09:41:24 [INFO] [app.core.alert_detector] [_load_alerts:484] - 已加载 0 个当前告警
2025-09-20 09:41:24 [INFO] [app.core.alert_detector] [_load_alerts:491] - 已加载 370 个历史告警
2025-09-20 09:41:24 [INFO] [app.core.alert_detector] [_load_notification_channels:502] - 已加载 1 个通知渠道
2025-09-20 09:41:24 [INFO] [query_cache] [__init__:58] - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-09-20 09:41:24 [INFO] [alert_manager] [_init_alert_rules:315] - 已初始化默认告警规则
2025-09-20 09:41:24 [INFO] [alert_manager] [_init_notification_channels:333] - 已初始化默认通知渠道
2025-09-20 09:41:24 [INFO] [alert_manager] [__init__:258] - 告警管理器初始化完成
2025-09-20 09:41:25 [INFO] [db_service] [_create_engine:92] - 数据库引擎和会话工厂创建成功
2025-09-20 09:41:25 [INFO] [db_service] [__init__:56] - 数据库服务初始化完成
2025-09-20 09:41:25 [INFO] [notification_service] [__init__:55] - 通知服务初始化完成
2025-09-20 09:41:25 [INFO] [main] [<module>:56] - 错误处理模块导入成功
2025-09-20 09:41:25 [INFO] [main] [<module>:65] - 监控模块导入成功
2025-09-20 09:41:25 [INFO] [main] [<module>:75] - 不再使用迁移脚本，使用标准化的数据库模型
2025-09-20 09:41:25 [INFO] [error_handling] [setup_error_handling:271] - 错误处理已设置
2025-09-20 09:41:25 [INFO] [main] [<module>:88] - 错误处理系统已设置
2025-09-20 09:41:26 [INFO] [main] [startup_event:399] - 应用启动中...
2025-09-20 09:41:26 [INFO] [main] [startup_event:402] - 错误处理系统已在应用创建时初始化
2025-09-20 09:41:26 [INFO] [monitoring] [init_monitoring:441] - 添加指标端点成功: /metrics
2025-09-20 09:41:26 [INFO] [monitoring] [init_monitoring:448] - 添加健康检查端点成功: /health
2025-09-20 09:41:26 [INFO] [monitoring] [init_monitoring:454] - 添加详细健康检查端点成功: /api/health/detailed
2025-09-20 09:41:26 [INFO] [monitoring] [init_system_info:103] - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-09-20 09:41:26 [INFO] [monitoring] [init_monitoring:464] - 启动资源监控线程成功
2025-09-20 09:41:26 [INFO] [monitoring] [init_monitoring:466] - 监控系统初始化成功（不使用中间件）
2025-09-20 09:41:26 [INFO] [monitoring] [init_monitoring:470] - 监控系统初始化完成
2025-09-20 09:41:26 [INFO] [main] [startup_event:409] - 监控系统初始化完成
2025-09-20 09:41:26 [INFO] [app.db.init_db] [<module>:45] - 所有模型导入成功
2025-09-20 09:41:26 [INFO] [app.db.init_db] [<module>:53] - 使用sha256_crypt进行密码哈希和验证
2025-09-20 09:41:26 [INFO] [db_service] [get_session:114] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:41:26 [INFO] [app.db.init_db] [init_db:67] - 所有模型导入成功
2025-09-20 09:41:26 [INFO] [app.db.init_db] [init_db:71] - 使用sha256_crypt进行密码哈希和验证
2025-09-20 09:41:26 [INFO] [app.db.init_db] [init_db:74] - 正在运行数据库迁移...
2025-09-20 09:41:26 [INFO] [app.db.init_db] [init_db:93] - 正在检查并更新数据库表结构...
2025-09-20 09:41:26 [INFO] [app.db.init_db] [init_db:97] - 数据库表结构检查和更新完成
2025-09-20 09:41:26 [INFO] [app.db.init_db] [_setup_model_relationships:120] - 模型关系初始化完成
2025-09-20 09:41:26 [INFO] [app.db.init_db] [init_db:101] - 模型关系设置完成
2025-09-20 09:41:26 [INFO] [main] [startup_event:418] - 数据库初始化完成（强制重建）
2025-09-20 09:41:26 [INFO] [db_service] [get_session:114] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:41:26 [INFO] [main] [startup_event:425] - 数据库连接正常
2025-09-20 09:41:26 [INFO] [main] [startup_event:433] - 开始初始化模板数据
2025-09-20 09:41:26 [INFO] [db_service] [get_session:114] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:41:27 [INFO] [app.db.init_templates] [init_assessment_templates:33] - 开始初始化评估量表模板...
2025-09-20 09:41:27 [INFO] [app.db.init_templates] [init_assessment_templates:72] - 成功初始化 5 个评估量表模板
2025-09-20 09:41:27 [INFO] [app.db.init_templates] [init_questionnaire_templates:85] - 开始初始化调查问卷模板...
2025-09-20 09:41:27 [INFO] [app.db.init_templates] [init_questionnaire_templates:116] - 成功初始化 5 个调查问卷模板
2025-09-20 09:41:27 [INFO] [main] [startup_event:437] - 模板数据初始化完成
2025-09-20 09:41:27 [INFO] [main] [startup_event:442] - 数据库表结构已经标准化，不需要迁移
2025-09-20 09:41:27 [INFO] [main] [startup_event:445] - 应用启动完成
2025-09-20 09:47:24 [WARNING] [alert_manager] [_create_alert:649] - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-09-20 09:48:24 [WARNING] [alert_manager] [_create_alert:649] - 触发告警: disk_usage, 当前值: 92.2, 阈值: 90
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:53:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 09:53:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:53:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 09:53:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:53:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 09:53:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:53:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 09:53:28 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 09:53:28 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:53:28 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:53:29 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:53:29 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:53:29 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:53:29 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 09:53:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 09:53:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 09:53:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 09:53:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 09:53:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 09:53:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 09:53:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 09:53:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 09:53:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 09:53:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 09:53:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 09:53:29 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 09:53:29 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 09:53:29 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:53:29 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:53:29 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:53:29 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:53:29 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:53:29 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:53:29 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:53:29 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:53:29 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:53:43 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:53:43 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:53:43 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:53:43 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:53:44 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:53:44 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:53:44 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:53:44 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:53:44 [INFO] [app.core.db_connection] [connect:176] - 数据库连接已创建
2025-09-20 09:53:44 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:53:44 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:53:45 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:53:45 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:53:45 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:56:07 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 09:56:07 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:56:07 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 09:56:07 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:56:07 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 09:56:07 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:56:07 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 09:56:07 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 09:56:07 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:56:07 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:56:08 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 09:56:08 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:56:08 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:56:08 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 09:56:08 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 09:56:08 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 09:56:08 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 09:56:08 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 09:56:08 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 09:56:08 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 09:56:08 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 09:56:08 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 09:56:08 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 09:56:08 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 09:56:08 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 09:56:08 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 09:56:08 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 09:56:08 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:56:08 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:56:08 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:56:08 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:56:08 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:56:08 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 09:56:08 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:56:08 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:56:08 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:56:24 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:56:24 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:56:24 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:56:24 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:56:24 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:56:24 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:56:24 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:56:24 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:56:24 [INFO] [app.core.db_connection] [connect:176] - 数据库连接已创建
2025-09-20 09:56:24 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:56:24 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:56:25 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:56:25 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:56:25 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:56:35 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 09:56:35 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:56:35 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:56:35 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 09:56:36 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 09:56:36 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 09:56:36 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 09:56:36 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 09:56:36 [INFO] [app.core.db_connection] [connect:176] - 数据库连接已创建
2025-09-20 09:56:36 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 09:56:36 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 09:56:37 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 09:56:37 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 09:56:37 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:01:01 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 10:01:01 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:01:01 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 10:01:01 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:01:01 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 10:01:01 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 10:01:01 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:01:01 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:01:02 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 10:01:02 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:01:02 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 10:01:02 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 10:01:02 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:01:02 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:01:19 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 10:01:19 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:01:19 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:01:19 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:01:20 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 10:01:20 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:01:20 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:01:20 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 10:01:20 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 10:01:20 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 10:01:21 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 10:01:21 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 10:01:21 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:03:12 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 10:03:12 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:03:12 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 10:03:12 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:03:12 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 10:03:12 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:03:12 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 10:03:12 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 10:03:12 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:03:12 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:03:13 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:03:13 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:03:13 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:03:13 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 10:03:13 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 10:03:13 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 10:03:13 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 10:03:13 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 10:03:13 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 10:03:13 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 10:03:13 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 10:03:13 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 10:03:13 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 10:03:13 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 10:03:13 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 10:03:13 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 10:03:13 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 10:03:13 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:03:13 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:03:13 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:03:13 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:03:13 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:03:13 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:03:13 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:03:13 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:03:13 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:04:40 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 10:04:40 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:04:40 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 10:04:40 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:04:40 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 10:04:40 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:04:40 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 10:04:40 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:04:40 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 10:04:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 10:04:40 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:04:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:04:41 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:04:41 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:04:41 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:04:41 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:05:55 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 10:05:55 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:05:55 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 10:05:55 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:05:55 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 10:05:55 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 10:05:55 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:05:55 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:05:56 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 10:05:56 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:05:56 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 10:05:56 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 10:05:56 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:05:56 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:06:21 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 10:06:21 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:06:21 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:06:21 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:06:22 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 10:06:22 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:06:22 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:06:22 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 10:06:22 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 10:06:22 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 10:06:23 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 10:06:23 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 10:06:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:07:19 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:07:19 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:07:19 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:07:19 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 10:07:19 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 10:07:19 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:07:19 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:07:19 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:07:19 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:07:20 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 10:07:20 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:07:20 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 10:07:20 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:07:20 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 10:07:20 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:07:20 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 10:07:20 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 10:07:20 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:07:20 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:07:40 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 10:07:40 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:07:40 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:07:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:07:42 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/register/login ---
2025-09-20 10:07:42 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:07:42 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:07:42 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 10:07:42 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 10:07:42 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 10:07:43 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 10:07:43 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 10:07:43 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:12:22 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:12:22 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:12:22 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:12:22 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 10:12:22 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 10:12:22 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:12:22 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:12:22 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:12:22 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:12:22 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:12:22 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:12:22 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:12:22 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:12:22 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 10:12:22 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 10:12:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:12:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 10:12:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:12:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 10:12:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:12:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 10:12:23 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 10:12:23 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:12:23 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:12:24 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:12:24 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:12:24 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:12:24 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:20:57 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 10:20:57 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:20:57 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 10:20:57 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:20:57 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 10:20:57 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 10:20:57 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:20:57 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:20:58 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 10:20:58 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:20:58 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 10:20:58 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 10:20:58 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:20:58 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:22:46 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 10:22:46 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:22:46 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 10:22:46 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:22:46 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 10:22:46 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:22:46 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 10:22:46 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:22:46 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 10:22:46 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 10:22:46 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:22:46 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:22:47 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:22:47 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:22:47 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:22:47 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:25:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 10:25:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:25:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 10:25:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:25:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 10:25:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:25:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 10:25:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:25:31 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:25:31 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 10:25:31 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 10:25:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 10:25:32 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 10:25:32 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 10:25:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:25:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:25:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:25:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:25:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:25:32 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:25:32 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:25:32 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:25:32 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:25:50 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 10:25:50 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:25:50 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:25:50 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:25:52 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/login_json ---
2025-09-20 10:25:52 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:25:52 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:25:52 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 10:25:52 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 10:25:52 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 10:25:53 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 10:25:53 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 10:25:53 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:26:38 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: users, 记录数: 1
2025-09-20 10:26:38 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: users, 处理1条记录
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:26:38 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: health_records, 记录数: 4
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 91, 目标ID: 91
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 90, 目标ID: 90
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 8, 目标ID: 8
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: health_records, 原ID: 2, 目标ID: 2
2025-09-20 10:26:38 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: health_records, 处理4条记录
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:26:38 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: medications, 记录数: 13
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 13, 目标ID: 13
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 14, 目标ID: 14
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 15, 目标ID: 15
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 18, 目标ID: 18
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 19, 目标ID: 19
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 22, 目标ID: 22
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 31, 目标ID: 31
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 32, 目标ID: 32
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 24, 目标ID: 24
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 25, 目标ID: 25
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 26, 目标ID: 26
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 27, 目标ID: 27
2025-09-20 10:26:38 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: medications, 原ID: 28, 目标ID: 28
2025-09-20 10:26:38 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: medications, 处理13条记录
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:26:38 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:26:39 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:26:39 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:26:39 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:26:39 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:26:39 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: assessments, 记录数: 10
2025-09-20 10:26:39 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 11, 目标ID: 11
2025-09-20 10:26:39 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 1, 目标ID: 1
2025-09-20 10:26:39 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 2, 目标ID: 2
2025-09-20 10:26:39 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 3, 目标ID: 3
2025-09-20 10:26:39 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 4, 目标ID: 4
2025-09-20 10:26:39 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 5, 目标ID: 5
2025-09-20 10:26:39 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 6, 目标ID: 6
2025-09-20 10:26:39 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 7, 目标ID: 7
2025-09-20 10:26:39 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 9, 目标ID: 9
2025-09-20 10:26:39 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: assessments, 原ID: 10, 目标ID: 10
2025-09-20 10:26:39 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: assessments, 处理10条记录
2025-09-20 10:26:39 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:26:39 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:26:39 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:26:39 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:26:39 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:26:39 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/sync/batch ---
2025-09-20 10:26:39 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:26:39 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:26:39 [INFO] [app.api.sync.SyncManager] [batch_sync_records:568] - 开始批量同步: documents, 记录数: 12
2025-09-20 10:26:39 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 40, 目标ID: 40
2025-09-20 10:26:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 39, 目标ID: 39
2025-09-20 10:26:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 38, 目标ID: 38
2025-09-20 10:26:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 37, 目标ID: 37
2025-09-20 10:26:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 36, 目标ID: 36
2025-09-20 10:26:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 35, 目标ID: 35
2025-09-20 10:26:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 34, 目标ID: 34
2025-09-20 10:26:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 33, 目标ID: 33
2025-09-20 10:26:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 32, 目标ID: 32
2025-09-20 10:26:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 30, 目标ID: 30
2025-09-20 10:26:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 27, 目标ID: 27
2025-09-20 10:26:40 [WARNING] [app.api.sync.SyncManager] [_update_record:1015] - 尝试更新主键ID字段，已忽略。表: documents, 原ID: 28, 目标ID: 28
2025-09-20 10:26:40 [INFO] [app.api.sync.SyncManager] [batch_sync_records:708] - 批量同步成功: documents, 处理12条记录
2025-09-20 10:26:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:26:40 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:26:40 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:26:40 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:26:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:26:40 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/sync/records ---
2025-09-20 10:26:40 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:26:40 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:26:40 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:26:53 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: GET /api/health ---
2025-09-20 10:26:53 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:26:53 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:26:53 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---

2025-09-20 10:26:53 [INFO] [main] [auth_debug_middleware:106] - 
--- 请求开始: POST /api/auth/login_json ---
2025-09-20 10:26:53 [INFO] [main] [auth_debug_middleware:117] - 请求没有认证头部
2025-09-20 10:26:53 [INFO] [main] [auth_debug_middleware:227] - 没有认证头部，设置用户为None
2025-09-20 10:26:53 [INFO] [app.core.db_connection] [get_db_session:493] - 尝试使用db_connection获取会话
2025-09-20 10:26:53 [INFO] [app.core.db_connection] [get_session:216] - 数据库连接成功 (尝试 1/3)
2025-09-20 10:26:53 [INFO] [app.core.db_connection] [get_db_session:497] - 使用db_connection获取会话成功
2025-09-20 10:26:54 [WARNING] [app.core.security] [verify_password:184] - 简单哈希验证失败
2025-09-20 10:26:54 [WARNING] [app.core.security] [verify_password:189] - 所有密码验证方法都失败
2025-09-20 10:26:54 [INFO] [main] [auth_debug_middleware:235] - --- 请求结束: 200 ---


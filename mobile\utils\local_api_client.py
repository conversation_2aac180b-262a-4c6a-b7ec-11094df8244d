"""
本地API客户端 - 专门用于本地开发环境
"""
import os
import json
import time
import logging
import requests
from datetime import datetime

# 导入本地配置
from .local_config import SERVER_CONFIG, DEFAULT_SERVER, SERVER_PRIORITY, API_TIMEOUT, MAX_RETRIES
from .optimized_connection_detector import get_connection_detector
from .simple_fallback_manager import get_fallback_manager

# 配置日志 - 使用简单的日志配置
logger = logging.getLogger(__name__)
# 避免在模块级别调用任何可能导致递归的日志方法

class LocalApiClient:
    """本地API客户端 - 优先使用本地服务器，自动切换到备用服务器"""

    def __init__(self):
        """初始化本地API客户端"""
        # 服务器配置
        self.server_configs = SERVER_CONFIG
        self.server_priority = SERVER_PRIORITY

        # 当前使用的服务器
        self.current_server = DEFAULT_SERVER
        self.server_status = {}

        # 使用优化的连接检测器
        self.detector = get_connection_detector()

        # 使用简单的回退管理器
        self.fallback_manager = get_fallback_manager()

        # 初始化服务器状态
        for server_key in self.server_configs:
            self.server_status[server_key] = {
                "available": True,
                "last_check": 0,
                "failure_count": 0
            }

        # API设置
        self.timeout = API_TIMEOUT
        self.max_retries = MAX_RETRIES

        # 认证信息
        self.token = None
        self.user_id = None

        # 错误信息
        self.last_error = None

        # 数据目录
        self.data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)

        # 加载认证信息
        self._load_auth_info()

        # 记录初始化信息（使用debug级别减少重复日志）
        current_config = self.server_configs[self.current_server]
        logger.debug(f"初始化本地API客户端，当前服务器: {current_config['name']} ({current_config['url']})")

    def _get_server_url(self, server_key=None):
        """获取服务器URL"""
        server = server_key or self.current_server
        config = self.server_configs[server]
        return config["url"]

    def _get_api_url(self, endpoint, server_key=None):
        """构建API URL"""
        server = server_key or self.current_server
        config = self.server_configs[server]
        base_url = config["url"]
        api_prefix = config["api_prefix"]

        # 确保endpoint不以/开头
        clean_endpoint = endpoint.lstrip('/')

        # 构建完整URL
        url = f"{base_url}/{api_prefix}/{clean_endpoint}"
        return url

    def _switch_server(self):
        """切换到下一个可用的服务器"""
        failed_server = self.current_server
        
        # 标记当前服务器失败
        self.server_status[failed_server]["failure_count"] += 1
        self.server_status[failed_server]["last_check"] = time.time()

        # 使用回退管理器处理连接失败
        suggested_server = self.fallback_manager.handle_connection_failure(failed_server)
        
        if suggested_server and suggested_server != self.current_server:
            # 检查建议的服务器是否在配置中
            if suggested_server in self.server_configs:
                old_server = self.current_server
                self.current_server = suggested_server
                logger.info(f"回退管理器建议切换服务器: {self.server_configs[old_server]['name']} -> {self.server_configs[suggested_server]['name']}")
                
                # 重置新服务器的失败计数
                if suggested_server in self.server_status:
                    self.server_status[suggested_server]["failure_count"] = 0
                    self.server_status[suggested_server]["available"] = True
                return True
            else:
                logger.warning(f"回退管理器建议的服务器 {suggested_server} 不在配置中")
        
        # 提高失败阈值，减少频繁切换（从5次提高到10次）
        if self.server_status[failed_server]["failure_count"] >= 10:
            self.server_status[failed_server]["available"] = False
            logger.warning(f"服务器 {self.server_configs[failed_server]['name']} 连续失败10次，标记为不可用")

        # 按优先级查找下一个可用的服务器
        for server_key in self.server_priority:
            if server_key != failed_server and self.server_status[server_key]["available"]:
                # 切换到新服务器
                old_server = self.current_server
                self.current_server = server_key
                logger.info(f"切换服务器: {self.server_configs[old_server]['name']} -> {self.server_configs[self.current_server]['name']}")
                return True

        # 如果没有可用的服务器，保持当前服务器
        logger.error("没有可用的服务器")
        return False

    def _make_request(self, method, endpoint, params=None, data=None, json_data=None,
                     files=None, headers=None, timeout=None, max_retries=None):
        """发送API请求"""
        # 使用默认值或指定值
        timeout = timeout or self.timeout
        max_retries = max_retries if max_retries is not None else self.max_retries

        # 准备请求参数
        kwargs = {'timeout': timeout}
        if params:
            kwargs['params'] = params
        if data:
            kwargs['data'] = data
        if json_data:
            kwargs['json'] = json_data
        if files:
            kwargs['files'] = files

        # 明确禁用代理，避免代理连接问题
        kwargs['proxies'] = {
            'http': None,
            'https': None
        }

        # 添加认证头
        if self.token:
            headers = headers or {}
            headers["Authorization"] = f"Bearer {self.token}"

        if headers:
            kwargs['headers'] = headers

        # 重试逻辑
        retry_count = 0
        tried_servers = set()

        while retry_count <= max_retries:
            # 构建URL
            url = self._get_api_url(endpoint)

            # 记录重试信息
            retry_info = "" if retry_count == 0 else f"(重试 {retry_count}/{max_retries})"
            logger.info(f"发送 {method.upper()} 请求 {retry_info}: {url}")

            try:
                # 发送请求
                response = getattr(requests, method.lower())(url, **kwargs)

                # 处理响应
                if response.status_code == 200:
                    try:
                        # 尝试解析JSON响应
                        result = response.json()
                        # 重置失败计数
                        self.server_status[self.current_server]["failure_count"] = 0
                        
                        # 通知回退管理器连接成功
                        self.fallback_manager.handle_connection_success(self.current_server)

                        # 记录完整响应，用于调试
                        logger.debug(f"API响应: {result}")

                        return result
                    except ValueError:
                        # 如果无法解析为JSON，尝试获取文本内容
                        try:
                            text_content = response.text
                            logger.error(f"API响应不是有效的JSON: {text_content[:200]}...")

                            # 如果响应为空或只有空白字符，但状态码是200，认为请求成功
                            if not text_content.strip():
                                logger.info("API响应为空，但状态码为200，认为请求成功")
                                return {"success": True, "message": "请求成功，但响应为空"}

                            self.last_error = "响应解析错误"
                            return None
                        except Exception as text_error:
                            logger.error(f"获取响应文本时出错: {text_error}")
                            self.last_error = "响应解析错误"
                            return None
                else:
                    # 处理错误响应
                    self.last_error = f"请求错误: {response.status_code}"
                    logger.error(f"API请求错误: {response.status_code}")

                    # 尝试获取错误详情
                    try:
                        error_detail = response.json()
                        logger.error(f"错误详情: {error_detail}")
                    except:
                        try:
                            error_text = response.text
                            logger.error(f"错误文本: {error_text[:200]}...")
                        except:
                            pass

                    # 对于5xx错误，尝试切换服务器
                    if 500 <= response.status_code < 600:
                        # 记录当前服务器
                        tried_servers.add(self.current_server)

                        # 尝试切换服务器
                        if self._switch_server() and self.current_server not in tried_servers:
                            # 不增加重试计数，直接使用新服务器重试
                            continue

            except Exception as e:
                # 处理请求异常
                self.last_error = f"请求异常: {str(e)}"
                logger.error(f"API请求异常: {str(e)}")

                # 记录当前服务器
                tried_servers.add(self.current_server)

                # 尝试切换服务器
                if self._switch_server() and self.current_server not in tried_servers:
                    # 不增加重试计数，直接使用新服务器重试
                    continue

            # 增加重试计数
            retry_count += 1
            if retry_count <= max_retries:
                # 指数退避策略
                wait_time = min(2 ** retry_count, 20)
                logger.info(f"将在 {wait_time} 秒后重试请求")
                time.sleep(wait_time)
            else:
                logger.error(f"API请求失败，已达到最大重试次数: {max_retries}")
                return None

        # 所有重试后仍然失败
        return None

    def check_server_health(self):
        """检查所有服务器的健康状态"""
        results = {}
        any_server_online = False

        # 获取所有服务器URL
        server_urls = []
        server_keys = []
        for server_key, config in self.server_configs.items():
            server_urls.append(config['url'])
            server_keys.append(server_key)

        # 使用优化的连接检测器批量检测
        detection_results = self.detector.detect_multiple_servers(server_urls, f"/{self.server_configs[self.current_server]['api_prefix']}/health")

        # 处理检测结果
        for i, server_key in enumerate(server_keys):
            config = self.server_configs[server_key]
            server_url = server_urls[i]
            result = detection_results.get(server_url, {'online': False})

            if result['online']:
                # 服务器在线
                results[server_key] = {
                    "name": config["name"],
                    "url": config["url"],
                    "status": "online",
                    "message": "服务器连接正常"
                }
                # 重置失败计数
                self.server_status[server_key]["failure_count"] = 0
                self.server_status[server_key]["available"] = True
                any_server_online = True
            else:
                # 服务器离线
                results[server_key] = {
                    "name": config["name"],
                    "url": config["url"],
                    "status": "offline",
                    "message": result.get('error', '无法连接服务器')
                }
                self.server_status[server_key]["failure_count"] += 1

            # 更新最后检查时间
            self.server_status[server_key]["last_check"] = time.time()

            # 提高失败阈值，减少频繁切换（从5次提高到10次）
            if self.server_status[server_key]["failure_count"] >= 10:
                self.server_status[server_key]["available"] = False
                logger.warning(f"服务器 {config['name']} 连续失败10次，标记为不可用")

        # 如果当前服务器不可用，尝试切换到可用服务器
        if not self.server_status[self.current_server]["available"]:
            self._switch_server()

        return {
            "servers": results,
            "any_server_online": any_server_online,
            "current_server": self.server_configs[self.current_server]["name"]
        }

    def _save_auth_info(self):
        """保存认证信息到文件"""
        if not self.token:
            return

        auth_info = {
            "token": self.token,
            "user_id": self.user_id,
            "saved_at": datetime.now().isoformat()  # 使用ISO格式时间而不是时间戳
        }

        try:
            auth_file = os.path.join(self.data_dir, 'local_auth.json')
            with open(auth_file, 'w') as f:
                json.dump(auth_info, f)
            logger.debug("已保存认证信息")
        except Exception as e:
            logger.error(f"保存认证信息失败: {str(e)}")

    def _load_auth_info(self):
        """从文件加载认证信息"""
        try:
            auth_file = os.path.join(self.data_dir, 'local_auth.json')
            if os.path.exists(auth_file):
                with open(auth_file, 'r') as f:
                    auth_info = json.load(f)

                self.token = auth_info.get("token")
                self.user_id = auth_info.get("user_id")
                logger.debug("已加载认证信息")
        except Exception as e:
            logger.error(f"加载认证信息失败: {str(e)}")

    def register_user(self, user_data):
        """注册新用户"""
        # 适配数据格式
        from .api_adapters import adapt_register_data
        api_data = adapt_register_data(user_data)

        if not api_data:
            logger.error("无法适配用户数据")
            self.last_error = "数据格式不兼容"
            return None

        # 发送注册请求
        logger.info(f"向后端发送注册请求: {api_data.get('username')}")
        result = self._make_request(
            method="POST",
            endpoint="auth/register",
            json_data=api_data,
            timeout=30
        )

        if result:
            # 检查注册是否成功
            if result.get("status") == "success" or result.get("success"):
                logger.info(f"用户注册成功: {api_data.get('username')}")

                # 如果后端返回了用户ID，保存它
                if "user_id" in result:
                    user_data["user_id"] = result["user_id"]
                elif "id" in result:
                    user_data["user_id"] = result["id"]

                # 如果后端返回了访问令牌，保存它
                if "access_token" in result:
                    self.token = result["access_token"]
                    self._save_auth_info()

                return result
            else:
                # 注册失败，提取错误信息
                error_message = result.get("message", "未知错误")
                logger.error(f"用户注册失败: {error_message}")
                self.last_error = error_message
                return None
        else:
            # 请求失败
            logger.error(f"后端注册失败: {self.last_error or '未知错误'}")
            return None

    def login(self, username, password):
        """用户登录"""
        # 记录登录尝试
        logger.info(f"尝试登录用户: {username}")

        # 适配数据格式
        from .api_adapters import adapt_login_data
        login_data = adapt_login_data({
            "username": username,
            "password": password
        })

        # 首先尝试使用表单格式登录
        logger.info(f"尝试使用表单格式登录: {username}")
        result = self._make_request(
            method="POST",
            endpoint="auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )

        # 打印登录结果，用于调试
        if result:
            logger.info(f"表单登录结果: {result.get('status', 'unknown')}")
            if "error" in result:
                logger.info(f"表单登录错误: {result.get('error')}")

        # 如果表单登录失败，尝试使用JSON格式登录
        if not result or not (result.get("status") == "success" or result.get("success")):
            logger.info(f"表单登录失败，尝试使用JSON格式登录: {username}")
            result = self._make_request(
                method="POST",
                endpoint="auth/login_json",
                json_data={"username": username, "password": password}
            )

            # 打印登录结果，用于调试
            if result:
                logger.info(f"JSON登录结果: {result.get('status', 'unknown')}")
                if "error" in result:
                    logger.info(f"JSON登录错误: {result.get('error')}")

        # 如果JSON登录也失败，尝试使用简单登录
        if not result or not (result.get("status") == "success" or result.get("success")):
            logger.info(f"JSON登录失败，尝试使用简单登录: {username}")
            result = self._make_request(
                method="POST",
                endpoint="auth/simple_login",  # 修正端点路径，添加auth/前缀
                json_data={"username": username, "password": password}
            )

            # 打印登录结果，用于调试
            if result:
                logger.info(f"简单登录结果: {result.get('status', 'unknown')}")
                if "error" in result:
                    logger.info(f"简单登录错误: {result.get('error')}")

        # 如果所有登录方式都失败，尝试使用移动端专用登录
        if not result or not (result.get("status") == "success" or result.get("success")):
            logger.info(f"所有标准登录方式失败，尝试使用移动端专用登录: {username}")
            result = self._make_request(
                method="POST",
                endpoint="auth/login_json",  # 修正端点路径，使用正确的JSON登录端点
                json_data={"username": username, "password": password}
            )

            # 打印登录结果，用于调试
            if result:
                logger.info(f"移动端专用登录结果: {result.get('status', 'unknown')}")
                if "error" in result:
                    logger.info(f"移动端专用登录错误: {result.get('error')}")

        # 处理登录结果
        if result:
            # 打印完整的结果，用于调试
            logger.info(f"登录结果: {result}")

            # 检查是否成功
            is_success = result.get("status") == "success" or result.get("success")

            if is_success:
                # 提取数据
                data = result.get("data", {})
                if not data and isinstance(result, dict):
                    data = result

                # 尝试从不同位置获取令牌
                self.token = data.get("access_token", data.get("token", result.get("access_token", result.get("token"))))

                # 尝试从不同位置获取用户ID
                if "user" in data and isinstance(data["user"], dict):
                    # 如果用户信息在data.user中
                    self.user_id = data["user"].get("id", data["user"].get("user_id"))
                elif "user" in result and isinstance(result["user"], dict):
                    # 如果用户信息在result.user中
                    self.user_id = result["user"].get("id", result["user"].get("user_id"))
                else:
                    # 否则尝试直接获取
                    self.user_id = data.get("user_id", data.get("id", result.get("user_id", result.get("id"))))

                # 如果找到令牌，保存认证信息
                if self.token:
                    self._save_auth_info()
                    logger.info(f"用户登录成功: {username}, 令牌: {self.token[:10]}..., 用户ID: {self.user_id}")

                    # 获取用户信息
                    user_info = None
                    custom_id = None

                    if "user" in result and isinstance(result["user"], dict):
                        user_info = result["user"]
                        custom_id = user_info.get("custom_id")

                    # 如果没有custom_id，记录警告
                    if not custom_id:
                        logger.warning("没有custom_id，这可能导致某些功能无法正常工作")

                    # 构建标准化的返回数据
                    return {
                        "success": True,
                        "status": "success",
                        "message": "登录成功",
                        "access_token": self.token,
                        "token_type": "bearer",
                        "user_id": self.user_id,
                        "custom_id": custom_id,  # 添加custom_id
                        "username": username,
                        "user": user_info  # 添加完整的用户信息
                    }
                else:
                    # 如果没有令牌但登录成功，可能是后端没有返回令牌
                    logger.warning(f"登录成功但没有令牌: {username}")

                    # 尝试从用户信息中获取更多数据
                    user_info = None
                    if "user" in result and isinstance(result["user"], dict):
                        user_info = result["user"]
                    elif "user" in data and isinstance(data["user"], dict):
                        user_info = data["user"]

                    # 构建返回数据
                    return {
                        "success": True,
                        "status": "success",
                        "message": "登录成功但没有令牌",
                        "user_id": self.user_id,
                        "username": username,
                        "user": user_info
                    }
            else:
                # 登录失败，但有结果
                error_msg = result.get("message", "登录失败，未知错误")
                logger.error(f"登录失败: {error_msg}")
                self.last_error = error_msg

        # 登录失败，尝试本地认证
        logger.info(f"所有远程登录方式失败，尝试本地认证: {username}")
        try:
            # 导入用户管理器
            try:
                from .user_manager import get_user_manager
                user_manager = get_user_manager()
            except (ImportError, ModuleNotFoundError) as e:
                logger.error(f"无法导入用户管理器: {e}")
                return None

            # 尝试本地认证
            user = user_manager.authenticate(username, password)
            if user:
                logger.info(f"本地认证成功: {username}")
                # 获取custom_id
                custom_id = None
                if hasattr(user, 'custom_id') and user.custom_id:
                    custom_id = user.custom_id

                # 如果没有custom_id，记录警告
                if not custom_id:
                    logger.warning("用户没有custom_id，这可能导致某些功能无法正常工作")

                return {
                    "success": True,
                    "status": "success",
                    "message": "本地登录成功",
                    "user_id": user.user_id,
                    "custom_id": custom_id,  # 添加custom_id
                    "username": user.username,
                    "user": {  # 添加完整的用户信息
                        "id": user.user_id,
                        "username": user.username,
                        "full_name": user.full_name if hasattr(user, 'full_name') else "",
                        "role": user.role if hasattr(user, 'role') else "personal",
                        "custom_id": custom_id
                    }
                }
        except Exception as e:
            logger.error(f"本地认证失败: {str(e)}")

        # 所有认证方式都失败
        error_msg = result.get("message", "登录失败，未知错误") if result else "登录请求失败"
        logger.error(f"用户登录失败: {error_msg}")
        self.last_error = error_msg
        return None

    def logout(self):
        """用户退出登录"""
        if not self.token:
            return True

        # 发送登出请求
        self._make_request(
            method="POST",
            endpoint="auth/logout"
        )

        # 清除认证信息
        self.token = None
        self.user_id = None

        # 删除认证信息文件
        try:
            auth_file = os.path.join(self.data_dir, 'local_auth.json')
            if os.path.exists(auth_file):
                os.remove(auth_file)
                logger.info("已删除认证信息文件")
        except Exception as e:
            logger.error(f"删除认证信息文件失败: {str(e)}")

        return True

    def is_authenticated(self):
        """检查是否已认证"""
        return self.token is not None

    def get_user_info(self):
        """获取当前登录用户的信息"""
        if not self.token:
            logger.error("未登录，无法获取用户信息")
            return None

        result = self._make_request(
            method="GET",
            endpoint="users/me"
        )

        if result and (result.get("status") == "success" or result.get("success")):
            # 提取数据
            data = result.get("data", {})
            if not data and isinstance(result, dict):
                data = result
            return data

        return None

    def get_user_data(self, user_id=None):
        """获取用户数据，兼容APIClient接口

        Args:
            user_id: 用户ID，如果为None则获取当前用户信息

        Returns:
            dict: 用户数据，标准化格式
        """
        # 获取用户信息
        user_info = self.get_user_info()

        if user_info:
            # 获取或生成custom_id
            custom_id = user_info.get("custom_id")
            user_id = user_info.get("user_id", user_info.get("id", self.user_id))

            # 如果没有custom_id，记录警告
            if not custom_id:
                logger.warning("用户信息中没有custom_id，这可能导致某些功能无法正常工作")

            # 转换为标准API格式
            return {
                "id": user_id,
                "username": user_info.get("username", ""),
                "fullName": user_info.get("full_name", user_info.get("real_name", "")),
                "email": user_info.get("email", ""),
                "role": user_info.get("role", user_info.get("identity", "")),
                "status": "active",
                "created_at": user_info.get("created_at", ""),
                "custom_id": custom_id  # 添加custom_id
            }
        else:
            # 如果无法获取用户信息，但有用户ID，返回基本信息
            if self.user_id:
                logger.warning("无法获取完整用户信息，只返回基本信息")

                return {
                    "id": self.user_id,
                    "username": "",
                    "fullName": "",
                    "email": "",
                    "role": "",
                    "status": "active",
                    "created_at": "",
                    "error": "无法获取完整用户信息"
                }

            logger.error("获取用户数据失败")
            return {"error": "获取用户数据失败"}

    def upload_file(self, file_path, metadata=None, file_type=None, document_type=None, description=None):
        """上传文件到后端服务器"""
        if not self.token:
            logger.error("未登录，无法上传文件")
            return None

        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return None

        try:
            # 准备文件对象
            with open(file_path, 'rb') as f:
                file_content = f.read()

            file_name = os.path.basename(file_path)
            logger.info(f"准备上传文件: {file_name}, 大小: {len(file_content)} 字节")

            # 准备元数据
            from .api_adapters import adapt_document_upload_data
            from types import SimpleNamespace

            file_obj = SimpleNamespace()
            file_obj.name = file_name
            file_obj.content_type = self._get_mime_type(file_path)

            # 准备元数据
            meta = metadata or {}
            if file_type and 'file_type' not in meta:
                meta['file_type'] = file_type
            if document_type and 'document_type' not in meta:
                meta['document_type'] = document_type
            if description and 'description' not in meta:
                meta['description'] = description

            # 添加来源标记
            meta['source'] = 'mobile'

            # 添加时间戳
            if 'created_at' not in meta:
                meta['created_at'] = datetime.now().isoformat()

            # 保守清理元数据：防止把后端不接受的字段上传
            # 将 patient_id 映射为 custom_id（如果没有 custom_id）并移除 patient_id
            if 'patient_id' in meta:
                if 'custom_id' not in meta:
                    meta['custom_id'] = meta.get('patient_id')
                meta.pop('patient_id', None)

            # 移除客户端专有或后端可能拒绝的字段
            for banned in ['is_current', 'record_type', 'patient_id']:
                if banned in meta:
                    meta.pop(banned, None)

            # 确保 custom_id 存在（使用客户端实例上的 custom_id 作为回退）
            if 'custom_id' not in meta and hasattr(self, 'custom_id') and self.custom_id:
                meta['custom_id'] = self.custom_id

            # 适配数据格式
            form_data, files = adapt_document_upload_data(meta, file_obj)

            # 替换文件内容
            files['file'] = (file_name, file_content, file_obj.content_type)

            # 发送上传请求
            logger.info(f"发送文件上传请求: {file_name}, 元数据: {meta}")
            result = self._make_request(
                method="POST",
                endpoint="mobile/upload",
                files=files,
                data=form_data,
                timeout=120  # 增加超时时间，因为文件上传可能需要较长时间
            )

            # 处理响应结果
            if result is not None:
                # 记录完整响应，用于调试
                logger.debug(f"文件上传响应: {result}")

                # 检查是否成功 - 支持多种成功标志
                is_success = False

                # 检查常见的成功标志
                if result.get("status") == "success" or result.get("success"):
                    is_success = True
                # 检查HTTP 200但没有明确成功标志的情况
                elif isinstance(result, dict):
                    # 如果响应是字典且没有明确的错误信息，认为成功
                    if "error" not in result and "message" not in result:
                        is_success = True
                    # 如果响应包含id或file_id，认为成功
                    elif "id" in result or "file_id" in result or "document_id" in result:
                        is_success = True

                if is_success:
                    logger.info(f"文件上传成功: {file_name}")

                    # 提取数据
                    data = result.get("data", {})
                    if not data and isinstance(result, dict):
                        data = result

                    # 如果data是空字典但result不是，使用result作为data
                    if not data and isinstance(result, dict) and result:
                        data = result

                    # 确保有文件ID - 支持多种ID字段名
                    file_id = None
                    for id_field in ["file_id", "id", "document_id"]:
                        if id_field in data:
                            file_id = data[id_field]
                            break

                    # 如果在data中找不到ID，尝试从result中获取
                    if not file_id:
                        for id_field in ["file_id", "id", "document_id"]:
                            if id_field in result:
                                file_id = result[id_field]
                                break

                    # 如果找到ID，确保返回的数据包含标准化的file_id字段
                    if file_id:
                        if isinstance(data, dict):
                            data["file_id"] = file_id
                        else:
                            # 如果data不是字典，创建一个新字典
                            data = {"file_id": file_id}

                        # 添加成功标志
                        data["success"] = True
                        return data
                    else:
                        # 如果没有找到ID但认为成功，返回一个基本的成功响应
                        logger.warning(f"文件上传成功，但未找到文件ID: {file_name}")
                        return {"success": True, "message": "文件上传成功，但未返回文件ID"}
                else:
                    # 上传失败，提取错误信息
                    error_msg = result.get("message", "上传失败，未知错误") if result else "上传请求失败"
                    if "error" in result:
                        error_msg = result["error"]

                    logger.error(f"文件上传失败: {error_msg}")
                    self.last_error = error_msg
                    return {"success": False, "message": error_msg}
            else:
                # 请求失败或无法解析响应
                error_msg = self.last_error or "上传请求失败"
                logger.error(f"文件上传失败: {error_msg}")
                return {"success": False, "message": error_msg}

        except Exception as e:
            error_msg = f"上传文件时出错: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.last_error = error_msg
            return None

    def _get_mime_type(self, file_path):
        """获取文件MIME类型"""
        import mimetypes
        mime_type, _ = mimetypes.guess_type(file_path)
        return mime_type or 'application/octet-stream'

    def upload_certificate(self, file_path, certificate_type, description=None):
        """上传资格证书到后端服务器

        Args:
            file_path (str): 文件路径
            certificate_type (str): 证书类型，如medical_license, practice_license
            description (str, optional): 证书描述

        Returns:
            dict: 上传结果，包含document_id
        """
        if not self.token:
            logger.error("未登录，无法上传证书")
            self.last_error = "未登录，无法上传证书"
            return None

        if not os.path.exists(file_path):
            logger.error(f"证书文件不存在: {file_path}")
            self.last_error = f"证书文件不存在: {file_path}"
            return None

        try:
            # 准备元数据
            metadata = {
                'file_type': 'certificate',
                'document_type': certificate_type,
                'description': description or f"{certificate_type} certificate",
                'source': 'mobile'
            }

            # 使用upload_file方法上传文件
            logger.info(f"上传证书: {certificate_type}, 文件: {file_path}")

            # 添加调试日志，记录请求详情
            logger.debug(f"证书上传请求详情: 文件类型={certificate_type}, 描述={description}, 元数据={metadata}")

            # 调用upload_file方法
            result = self.upload_file(
                file_path=file_path,
                metadata=metadata,
                file_type='certificate',
                document_type=certificate_type,
                description=description
            )

            # 记录原始响应
            logger.debug(f"证书上传原始响应: {result}")

            # 处理上传结果
            if result:
                # 检查result是否已经包含success字段
                if 'success' in result:
                    # 如果已经包含success字段，检查是否成功
                    if result['success']:
                        # 确保有document_id字段
                        document_id = result.get('file_id') or result.get('id') or result.get('document_id')
                        if document_id:
                            return {
                                'success': True,
                                'document_id': document_id,
                                'message': result.get('message', '证书上传成功')
                            }
                        else:
                            # 成功但没有ID，可能是后端没有返回ID
                            logger.warning("证书上传成功，但未返回文档ID")
                            return {
                                'success': True,
                                'message': '证书上传成功，但未返回文档ID'
                            }
                    else:
                        # 上传失败，返回错误信息
                        error_msg = result.get('message', '证书上传失败，未知错误')
                        logger.error(f"证书上传失败: {error_msg}")
                        return {
                            'success': False,
                            'message': error_msg
                        }
                else:
                    # 没有success字段，尝试从其他字段判断是否成功
                    # 检查是否有ID字段
                    document_id = None
                    for id_field in ['file_id', 'id', 'document_id']:
                        if id_field in result:
                            document_id = result[id_field]
                            break

                    if document_id:
                        # 有ID字段，认为上传成功
                        return {
                            'success': True,
                            'document_id': document_id,
                            'message': '证书上传成功'
                        }
                    elif 'error' in result or 'message' in result and '失败' in result.get('message', ''):
                        # 有错误信息，认为上传失败
                        error_msg = result.get('error') or result.get('message', '证书上传失败，未知错误')
                        logger.error(f"证书上传失败: {error_msg}")
                        return {
                            'success': False,
                            'message': error_msg
                        }
                    else:
                        # 没有明确的成功或失败标志，但有响应，认为成功
                        logger.info("证书上传可能成功，但响应格式不标准")
                        return {
                            'success': True,
                            'message': '证书上传可能成功，但响应格式不标准',
                            'raw_response': result
                        }
            else:
                # 上传失败或没有响应
                error_msg = self.last_error or "证书上传失败，未知错误"
                logger.error(f"证书上传失败: {error_msg}")
                return {
                    'success': False,
                    'message': error_msg
                }

        except Exception as e:
            error_msg = f"上传证书时出错: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.last_error = error_msg
            return {
                'success': False,
                'message': error_msg
            }

    def request_ocr(self, file_id, options=None):
        """请求对文件进行OCR处理"""
        if not self.token:
            logger.error("未登录，无法请求OCR处理")
            return None

        # 准备OCR选项
        ocr_options = options or {}

        # 确保有默认值
        if "detect_orientation" not in ocr_options:
            ocr_options["detect_orientation"] = True

        if "language" not in ocr_options:
            ocr_options["language"] = "chi_sim"  # 默认使用简体中文

        # 发送OCR处理请求
        logger.info(f"发送OCR处理请求: 文件ID={file_id}, 选项={ocr_options}")
        result = self._make_request(
            method="POST",
            endpoint=f"documents/{file_id}/ocr",
            json_data=ocr_options,
            timeout=60  # 增加超时时间，因为OCR处理可能需要较长时间
        )

        if result and (result.get("status") == "success" or result.get("success")):
            logger.info(f"OCR处理请求成功: {file_id}")

            # 提取数据
            data = result.get("data", {})
            if not data and isinstance(result, dict):
                data = result

            # 确保返回任务ID
            if "task_id" not in data and "id" in data:
                data["task_id"] = data["id"]

            return data
        else:
            error_msg = result.get("message", "OCR请求失败，未知错误") if result else "OCR请求失败"
            logger.error(f"OCR处理请求失败: {error_msg}")
            self.last_error = error_msg
            return None

    def get_ocr_result(self, file_id):
        """获取OCR处理结果"""
        if not self.token:
            logger.error("未登录，无法获取OCR结果")
            return None

        # 发送获取OCR结果请求
        logger.info(f"获取OCR处理结果: 文件ID={file_id}")
        result = self._make_request(
            method="GET",
            endpoint=f"documents/{file_id}/ocr-result",
            timeout=30  # 增加超时时间
        )

        if result and (result.get("status") == "success" or result.get("success")):
            # 提取数据
            data = result.get("data", {})
            if not data and isinstance(result, dict):
                data = result

            # 检查OCR状态
            status = data.get("ocr_status", data.get("status", "unknown"))

            # 标准化状态值
            if status in ["completed", "success", "done"]:
                logger.info(f"OCR处理已完成: {file_id}")
                data["status"] = "completed"
            elif status in ["processing", "pending", "in_progress"]:
                logger.info(f"OCR处理中: {file_id}, 进度: {data.get('progress', '未知')}%")
                data["status"] = "processing"
            elif status in ["failed", "error"]:
                logger.warning(f"OCR处理失败: {file_id}, 错误: {data.get('error', '未知错误')}")
                data["status"] = "failed"
            else:
                logger.warning(f"OCR状态未知: {status}")
                data["status"] = "unknown"

            # 确保有文本字段
            if "text" not in data and "ocr_text" in data:
                data["text"] = data["ocr_text"]

            # 确保有提取字段数据
            if "extracted_fields" not in data and "fields" in data:
                data["extracted_fields"] = data["fields"]

            return data
        else:
            error_msg = result.get("message", "获取OCR结果失败，未知错误") if result else "获取OCR结果失败"
            logger.error(f"获取OCR结果失败: {error_msg}")
            self.last_error = error_msg
            return None

"""CloudAPI常量定义模块

定义CloudAPI中使用的常量，避免重复字符串
"""

# HTTP相关常量
CONTENT_TYPE_JSON = 'application/json'
AUTHORIZATION_BEARER = 'Bearer'
HEADER_X_USER_ID = 'X-User-ID'
HEADER_AUTHORIZATION = 'Authorization'
HEADER_CONTENT_TYPE = 'Content-Type'

# 状态和消息常量
STATUS_SUCCESS = 'success'
STATUS_ERROR = 'error'
STATUS_KEY = 'status'
MESSAGE_KEY = 'message'
DETAIL_KEY = 'detail'
ERROR_STATUS = 'error'
SUCCESS_STATUS = 'success'
MSG_VALIDATION_FAILED = '请求数据验证失败'
MSG_DICT_RESPONSE_SUCCESS = '检测到字典格式响应，假设为成功'
MSG_DICT_RESPONSE_ASSUMED_SUCCESS = '检测到字典格式响应，假设为成功'
MSG_SERVER_OFFLINE = '服务器离线'
MSG_REQUEST_FAILED = '请求失败'
MSG_UNKNOWN_ERROR = '未知错误'

# API端点常量
ENDPOINT_AUTH_LOGIN = 'auth/login_json'
ENDPOINT_AUTH_REFRESH = 'auth/refresh'
ENDPOINT_AUTH_LOGOUT = 'auth/logout'
ENDPOINT_DOCUMENTS = 'documents'
ENDPOINT_USER_HEALTH_RECORDS = 'user-health-records'
ENDPOINT_MOBILE_TEMPLATES = 'mobile/templates/assessment-templates'
ENDPOINT_MOBILE_ASSESSMENTS = 'mobile/assessments'
ENDPOINT_MOBILE_QUESTIONNAIRES = 'mobile/questionnaires'
ENDPOINT_MOBILE_API_NEW_ASSESSMENTS = 'mobile_api_new/assessments'
ENDPOINT_MOBILE_API_NEW_QUESTIONNAIRES = 'mobile_api_new/questionnaires'

# 文件相关常量
SUPPORTED_FILE_FORMATS = ['.jpg', '.jpeg', '.png', '.pdf', '.doc', '.docx', '.xls', '.xlsx']
DATA_DIR_NAME = 'data'
QUEUE_DIR_NAME = 'upload_queue'
AUTH_FILE_NAME = 'cloud_auth.json'

# 服务器配置常量
DEFAULT_TIMEOUT = 30
DEFAULT_RETRY_COUNT = 3
MAX_FAILURE_COUNT = 3
DEGRADED_MODE_DURATION = 300  # 5分钟
FAILURE_COUNT_KEY = 'failure_count'

# 代理相关常量
PROXY_KEYWORDS = ['proxy', '127.0.0.1:7890']
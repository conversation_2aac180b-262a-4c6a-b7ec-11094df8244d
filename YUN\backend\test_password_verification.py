#!/usr/bin/env python3
"""
测试密码验证脚本
"""

from passlib.context import CryptContext

def test_password_verification():
    """测试密码验证"""
    
    # 后端数据库中markey用户的密码哈希
    stored_hash = "$5$rounds=535000$dGnV9R0y4at7E3nF$WOdH8NuTibm61p3TKs3MNqyZAQPamKfLWZWzy0hZz/3"
    
    # 可能的密码
    possible_passwords = [
        "markey0308@163",
        "markey0308",
        "123456",
        "password",
        "admin123",
        "markey123"
    ]
    
    # 创建密码上下文
    pwd_context = CryptContext(schemes=["sha256_crypt", "bcrypt"], deprecated="auto")
    
    print("测试密码验证...")
    print(f"存储的哈希: {stored_hash}")
    print()
    
    for password in possible_passwords:
        try:
            result = pwd_context.verify(password, stored_hash)
            print(f"密码 '{password}': {'✓ 验证成功' if result else '✗ 验证失败'}")
        except Exception as e:
            print(f"密码 '{password}': ✗ 验证出错 - {e}")
    
    print()
    print("测试生成新的哈希...")
    test_password = "markey0308@163"
    new_hash = pwd_context.hash(test_password)
    print(f"新生成的哈希: {new_hash}")
    
    # 验证新生成的哈希
    verify_result = pwd_context.verify(test_password, new_hash)
    print(f"新哈希验证结果: {'✓ 成功' if verify_result else '✗ 失败'}")

if __name__ == "__main__":
    test_password_verification()

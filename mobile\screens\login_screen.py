from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty
from mobile.screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
import os
import json
import logging
from datetime import datetime

# 获取日志记录器
logger = logging.getLogger(__name__)

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.textfield import (
    MDTextField,
    MDTextFieldLeadingIcon,
    MDTextFieldHintText,
    MDTextFieldHelperText,
    MDTextFieldTrailingIcon,
    MDTextFieldMaxLengthText
)
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.dropdownitem.dropdownitem import MDDropDownItem, MDDropDownItemText
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.scrollview import ScrollView
from kivymd.uix.card import MDCard
from kivy.uix.widget import Widget

# 导入主题和字体样式
try:
    from mobile.theme import AppTheme, AppMetrics, FontStyles, FontManager
    app_theme = AppTheme
except ImportError:
    # 回退配置
    class AppTheme:
        PRIMARY_COLOR = [0.133, 0.46, 0.82, 1]
        ACCENT_COLOR = [1, 0.25, 0.5, 1]
        TEXT_LIGHT = [1, 1, 1, 1]
        ERROR_COLOR = [0.96, 0.26, 0.21, 1]
        SUCCESS_COLOR = [0.30, 0.69, 0.31, 1]
        
        class DIMENSIONS:
            text_field_height = dp(56)
            
    app_theme = AppTheme

# 导入云端API
try:
    from mobile.utils.cloud_api import get_cloud_api
except ImportError:
    def get_cloud_api():
        return None

# 导入安全配置
try:
    from mobile.utils.security_config import validate_password_strength, login_limiter
except ImportError:
    def validate_password_strength():
        return True, ""
    
    class login_limiter:
        @staticmethod
        def is_allowed(username):
            return True
        
        @staticmethod
        def record_attempt(username, success):
            pass

import time

# 定义KV语言字符串 - 优化版本，符合KivyMD 2.0.1 dev0规范
KV = '''
<LoginTab>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    spacing: dp(16)
    padding: [0, dp(8), 0, 0]
    adaptive_height: True

    MDTextField:
        id: username
        mode: "outlined"
        size_hint_y: None
        height: dp(56)
        font_size: dp(16)

        MDTextFieldLeadingIcon:
            icon: "account"
            theme_icon_color: "Custom"
            icon_color: [0.133, 0.46, 0.82, 1]

        MDTextFieldHintText:
            text: "请输入用户名/手机号"

    MDTextField:
        id: password
        mode: "outlined"
        size_hint_y: None
        height: dp(56)
        font_size: dp(16)
        password: True

        MDTextFieldLeadingIcon:
            icon: "lock"
            theme_icon_color: "Custom"
            icon_color: [0.133, 0.46, 0.82, 1]

        MDTextFieldHintText:
            text: "请输入密码"

        MDTextFieldTrailingIcon:
            id: password_toggle_btn
            icon: "eye-off"
            theme_icon_color: "Custom"
            icon_color: [0.133, 0.46, 0.82, 1]

<PhoneTab>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    spacing: dp(16)
    padding: [0, dp(8), 0, 0]
    adaptive_height: True

    MDTextField:
        id: phone
        mode: "outlined"
        size_hint_y: None
        height: dp(56)
        font_size: dp(16)
        input_filter: "int"

        MDTextFieldLeadingIcon:
            icon: "phone"
            theme_icon_color: "Custom"
            icon_color: [0.133, 0.46, 0.82, 1]

        MDTextFieldHintText:
            text: "请输入手机号"

        MDTextFieldMaxLengthText:
            max_text_length: 11

    MDBoxLayout:
        size_hint_y: None
        height: dp(56)
        spacing: dp(8)

        MDTextField:
            id: verification_code
            mode: "outlined"
            size_hint_x: 0.65
            size_hint_y: None
            height: dp(56)
            font_size: dp(16)
            input_filter: "int"

            MDTextFieldLeadingIcon:
                icon: "numeric"
                theme_icon_color: "Custom"
                icon_color: [0.133, 0.46, 0.82, 1]

            MDTextFieldHintText:
                text: "请输入验证码"

            MDTextFieldMaxLengthText:
                max_text_length: 6

        MDButton:
            style: "filled"
            size_hint_x: 0.35
            height: dp(56)
            on_release: root.get_login_screen().on_request_verification_code()

            MDButtonText:
                text: "获取验证码"
                font_size: dp(12)
                theme_text_color: "Custom"
                text_color: [1, 1, 1, 1]

    # 保持高度一致的空白区域
    Widget:
        size_hint_y: None
        height: dp(20)

<LoginForm>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    spacing: dp(20)
    padding: [dp(16), dp(16), dp(16), dp(16)]

    MDCard:
        id: login_card
        orientation: 'vertical'
        size_hint_y: None
        height: self.minimum_height
        padding: dp(24)
        spacing: dp(20)
        elevation: 3
        radius: [dp(16)]
        md_bg_color: [1, 1, 1, 1]

        # 身份选择区域
        MDBoxLayout:
            id: identity_layout
            size_hint_y: None
            height: dp(56)
            spacing: dp(12)

            # 左侧空白区域，用于平衡布局
            Widget:
                size_hint_x: 0.3

            # 右侧身份选择按钮
            MDDropDownItem:
                id: identity_selector
                size_hint_x: 0.7
                size_hint_y: None
                height: dp(48)
                on_release: root.get_login_screen().show_identity_menu(self)

                MDDropDownItemText:
                    id: identity_text
                    text: "请选择登录身份"
                    font_size: dp(16)

        # 选项卡区域
        MDBoxLayout:
            id: tabs_layout
            size_hint_y: None
            height: dp(48)
            spacing: dp(8)

            MDButton:
                id: account_tab_btn
                style: "text"
                size_hint_x: 0.5
                on_release: root.get_login_screen().switch_tab("account")

                MDButtonText:
                    text: "账号登录"
                    theme_text_color: "Primary"
                    font_size: dp(16)
                    bold: True

            MDButton:
                id: phone_tab_btn
                style: "text"
                size_hint_x: 0.5
                on_release: root.get_login_screen().switch_tab("phone")

                MDButtonText:
                    text: "手机号登录"
                    theme_text_color: "Secondary"
                    font_size: dp(16)
                    bold: False

        # 选项卡指示器
        MDBoxLayout:
            id: tab_indicator
            size_hint_y: None
            height: dp(3)
            spacing: 0

            MDBoxLayout:
                id: account_indicator
                size_hint_x: 0.5
                md_bg_color: [0.133, 0.46, 0.82, 1]

            MDBoxLayout:
                id: phone_indicator
                size_hint_x: 0.5
                md_bg_color: [0, 0, 0, 0]

        # 选项卡内容区域
        MDBoxLayout:
            id: tab_content
            orientation: 'vertical'
            size_hint_y: None
            height: self.minimum_height
            spacing: dp(16)
            adaptive_height: True

        # 保留密码和自动登录选项
        MDBoxLayout:
            id: login_options_layout
            size_hint_y: None
            height: dp(40)
            spacing: dp(20)
            pos_hint: {"center_x": 0.5}

            MDBoxLayout:
                size_hint_x: None
                width: dp(120)
                spacing: dp(8)
                
                MDCheckbox:
                    id: remember_password_checkbox
                    size_hint: None, None
                    size: dp(24), dp(24)
                    active: False
                    theme_icon_color: "Custom"
                    icon_color: [0.133, 0.46, 0.82, 1]
                    on_active: root.get_login_screen().on_remember_password_changed(self.active)
                
                MDLabel:
                    text: "保留密码"
                    font_size: dp(14)
                    theme_text_color: "Secondary"
                    size_hint_y: None
                    height: dp(24)
                    valign: "center"

            MDBoxLayout:
                size_hint_x: None
                width: dp(120)
                spacing: dp(8)
                
                MDCheckbox:
                    id: auto_login_checkbox
                    size_hint: None, None
                    size: dp(24), dp(24)
                    active: False
                    theme_icon_color: "Custom"
                    icon_color: [0.133, 0.46, 0.82, 1]
                    on_active: root.get_login_screen().on_auto_login_changed(self.active)
                
                MDLabel:
                    text: "自动登录"
                    font_size: dp(14)
                    theme_text_color: "Secondary"
                    size_hint_y: None
                    height: dp(24)
                    valign: "center"

        # 登录按钮
        MDButton:
            id: login_button
            style: "filled"
            md_bg_color: [0.133, 0.46, 0.82, 1]
            size_hint_x: 0.85
            pos_hint: {"center_x": 0.5}
            height: dp(56)
            elevation: 3
            on_release: root.get_login_screen().on_login()

            MDButtonText:
                text: "登 录"
                font_size: dp(18)
                bold: True
                theme_text_color: "Custom"
                text_color: [1, 1, 1, 1]

    # 底部辅助操作区域
    MDBoxLayout:
        id: alternative_login_layout
        orientation: 'vertical'
        size_hint_y: None
        height: self.minimum_height
        spacing: dp(20)
        padding: [0, dp(16), 0, 0]

        MDLabel:
            text: "其他登录方式"
            halign: "center"
            theme_text_color: "Secondary"
            font_size: dp(14)
            size_hint_y: None
            height: dp(32)

        MDBoxLayout:
            size_hint_y: None
            height: dp(56)
            spacing: dp(40)
            pos_hint: {"center_x": 0.5}

            MDIconButton:
                icon: "wechat"
                icon_size: dp(36)
                theme_icon_color: "Custom"
                icon_color: [0.133, 0.46, 0.82, 1]
                on_release: root.get_login_screen().on_wechat_login()

            MDIconButton:
                icon: "fingerprint"
                icon_size: dp(36)
                theme_icon_color: "Custom"
                icon_color: [0.133, 0.46, 0.82, 1]
                on_release: root.get_login_screen().on_fingerprint_login()

            MDIconButton:
                icon: "face-recognition"
                icon_size: dp(36)
                theme_icon_color: "Custom"
                icon_color: [0.133, 0.46, 0.82, 1]
                on_release: root.get_login_screen().on_face_login()

        MDBoxLayout:
            size_hint_y: None
            height: dp(48)
            spacing: dp(8)
            pos_hint: {"center_x": 0.5}

            MDLabel:
                text: "新用户注册"
                font_size: dp(8)
                halign: "center"
                valign: "center"
                size_hint_y: None
                height: dp(48)
                theme_text_color: "Secondary"

            MDButton:
                style: "text"
                size_hint_x: None
                width: dp(80)
                on_release: root.get_login_screen().on_register()

                MDButtonText:
                    text: "立即注册"
                    theme_text_color: "Primary"
                    font_size: dp(8)

        MDLabel:
            text: "登录即代表同意《用户协议》和《隐私政策》"
            halign: "center"
            theme_text_color: "Secondary"
            font_size: dp(8)
            size_hint_y: None
            height: dp(32)
'''

# 注册KV语言
Builder.load_string(KV)

class LoginTab(MDBoxLayout):
    """账号登录选项卡 - 优化版本"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 设置自适应高度
        self.bind(minimum_height=self.setter('height'))
        # 延迟设置密码切换功能
        Clock.schedule_once(self._setup_password_toggle, 0.1)
        logger.debug("LoginTab初始化完成")

    def toggle_password_visibility(self):
        """切换密码可见性 - 优化版本"""
        try:
            password_field = self.ids.password
            password_toggle_btn = self.ids.password_toggle_btn

            if password_field.password:
                password_field.password = False
                password_toggle_btn.icon = "eye"
                logger.debug("密码已显示")
            else:
                password_field.password = True
                password_toggle_btn.icon = "eye-off"
                logger.debug("密码已隐藏")

        except Exception as e:
            logger.error(f"切换密码可见性失败: {e}")

    def _setup_password_toggle(self, dt):
        """设置密码切换功能"""
        try:
            # 获取密码切换按钮
            password_toggle_btn = self.ids.get('password_toggle_btn')
            if password_toggle_btn:
                # 在KivyMD 2.0.1中，使用on_press事件
                password_toggle_btn.bind(on_press=self._on_password_icon_touch)
                logger.debug("密码切换图标事件绑定成功")
            else:
                logger.warning("未找到密码切换图标")
        except Exception as e:
            logger.error(f"设置密码切换功能失败: {e}")

    def _on_password_icon_touch(self, instance):
        """处理密码图标点击事件"""
        try:
            self.toggle_password_visibility()
        except Exception as e:
            logger.error(f"处理密码图标点击事件失败: {e}")

class PhoneTab(MDBoxLayout):
    """手机号登录选项卡 - 优化版本"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 设置自适应高度
        self.bind(minimum_height=self.setter('height'))
        # 延迟初始化，确保组件完全加载
        Clock.schedule_once(self._post_init, 0.2)
        logger.debug("PhoneTab初始化完成")

    def _post_init(self, dt):
        """延迟初始化处理"""
        try:
            # 验证手机号输入框设置
            if hasattr(self.ids, 'phone'):
                self.ids.phone.bind(text=self._on_phone_text_change)
            logger.debug("手机号登录选项卡初始化完成")
        except Exception as e:
            logger.error(f"手机号选项卡初始化失败: {e}")

    def _on_phone_text_change(self, instance, text):
        """手机号输入变化处理"""
        # 限制只能输入数字，最多11位
        if text and not text.isdigit():
            instance.text = ''.join(filter(str.isdigit, text))
        if len(text) > 11:
            instance.text = text[:11]

    def get_login_screen(self):
        """获取LoginScreen实例"""
        # 向上遍历父组件找到LoginScreen
        parent = self.parent
        while parent:
            if hasattr(parent, '__class__') and parent.__class__.__name__ == 'LoginScreen':
                return parent
            parent = parent.parent
        return None

class LoginForm(MDBoxLayout):
    """登录表单组件 - 优化版本"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        logger.debug("登录表单组件初始化完成")

    def get_login_screen(self):
        """获取LoginScreen实例"""
        # 向上遍历父组件找到LoginScreen
        parent = self.parent
        while parent:
            if hasattr(parent, '__class__') and parent.__class__.__name__ == 'LoginScreen':
                return parent
            parent = parent.parent
        return None

class LoginScreen(BaseScreen):
    """登录屏幕 - 完全继承自BaseScreen基类"""

    current_tab = StringProperty("account")
    identity = StringProperty("")
    remember_password = BooleanProperty(False)
    auto_login = BooleanProperty(False)
    identity_menu = ObjectProperty(None)

    def __init__(self, **kwargs):
        # 设置BaseScreen属性
        kwargs['screen_title'] = "登录"
        kwargs['show_top_bar'] = False  # 登录页面不显示顶部导航栏
        super().__init__(**kwargs)

        # 初始化登录页面特有属性
        self.credentials_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../user_data.json")
        self.login_form = None
        self._auto_login_disabled = False  # 初始化自动登录禁用标志

        # 禁用自动登录，确保用户必须手动登录
        self._disable_auto_login_for_this_session()

    def _disable_auto_login_for_this_session(self):
        """禁用当前会话的自动登录"""
        try:
            # 设置自动登录禁用标志
            self._auto_login_disabled = True
            
            # 清除云端API的认证信息
            cloud_api = get_cloud_api()
            if cloud_api:
                cloud_api.token = None
                cloud_api.refresh_token_str = None
                cloud_api.user_id = None
                cloud_api.custom_id = None
                cloud_api.expires_at = None
                logger.info("已清除云端API认证信息，禁用自动登录")
        except Exception as e:
            logger.error(f"禁用自动登录时出错: {e}")

    def do_content_setup(self):
        """设置内容区域 - 完全遵循BaseScreen规范"""
        try:
            # 安全地获取content_container
            content_container = self.ids.get('content_container')
            if not content_container:
                logger.error("内容容器不存在，无法设置页面内容")
                return

            # 清空现有内容
            content_container.clear_widgets()

            # 创建主布局容器
            main_layout = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                height=dp(800),  # 设置足够的高度
                padding=[dp(16), dp(20), dp(16), dp(20)],
                spacing=dp(20)
            )
            main_layout.bind(minimum_height=main_layout.setter('height'))

            # 添加登录表单到主布局
            self.login_form = LoginForm()
            main_layout.add_widget(self.login_form)

            # 将主布局添加到content_container
            content_container.add_widget(main_layout)

            # 延迟初始化
            Clock.schedule_once(lambda dt: self.switch_tab("account"), 0.1)
            Clock.schedule_once(self.load_saved_credentials, 0.5)

            logger.info("登录页面内容设置完成")

        except Exception as e:
            logger.error(f"设置页面内容时出错: {e}")
            import traceback
            traceback.print_exc()

    def switch_tab(self, tab_name):
        """切换标签页 - 优化版本"""
        try:
            if not self.login_form:
                logger.warning("登录表单未初始化，无法切换选项卡")
                return

            self.current_tab = tab_name
            logger.debug(f"切换到选项卡: {tab_name}")

            # 更新选项卡内容
            content_area = self.login_form.ids.tab_content
            content_area.clear_widgets()

            # 添加对应的选项卡组件
            if tab_name == 'account':
                tab_widget = LoginTab()
            else:
                tab_widget = PhoneTab()

            content_area.add_widget(tab_widget)

            # 更新指示器和按钮状态
            self.update_tab_indicator()

        except Exception as e:
            logger.error(f"切换选项卡失败: {e}")
            import traceback
            traceback.print_exc()

    def update_tab_indicator(self):
        """更新选项卡指示器 - 优化版本"""
        try:
            if not self.login_form:
                logger.warning("登录表单未初始化，无法更新指示器")
                return

            # 使用固定的主题色
            primary_color = [0.133, 0.46, 0.82, 1]
            transparent = [0, 0, 0, 0]

            # 更新指示器颜色
            account_indicator = self.login_form.ids.get('account_indicator')
            phone_indicator = self.login_form.ids.get('phone_indicator')

            if account_indicator:
                account_indicator.md_bg_color = primary_color if self.current_tab == "account" else transparent

            if phone_indicator:
                phone_indicator.md_bg_color = primary_color if self.current_tab == "phone" else transparent

            # 更新按钮文本样式
            self._update_tab_button_style('account_tab_btn', self.current_tab == "account")
            self._update_tab_button_style('phone_tab_btn', self.current_tab == "phone")

            logger.debug(f"选项卡指示器更新完成: {self.current_tab}")

        except Exception as e:
            logger.error(f"更新选项卡指示器失败: {e}")

    def _update_tab_button_style(self, button_id, is_active):
        """更新选项卡按钮样式"""
        try:
            button = self.login_form.ids.get(button_id)
            if button:
                for child in button.children:
                    if isinstance(child, MDButtonText):
                        child.theme_text_color = "Primary" if is_active else "Secondary"
                        child.bold = is_active
        except Exception as e:
            logger.error(f"更新按钮样式失败 ({button_id}): {e}")

    def show_identity_menu(self, caller):
        """显示身份选择菜单 - 包含陪诊师身份"""
        menu_items = [
            {
                "text": "个人用户",
                "on_release": lambda x="个人用户": self.set_identity(x),
            },
            {
                "text": "单位管理员",
                "on_release": lambda x="单位管理员": self.set_identity(x),
            },
            {
                "text": "健康顾问",
                "on_release": lambda x="健康顾问": self.set_identity(x),
            },
            {
                "text": "陪诊师",
                "on_release": lambda x="陪诊师": self.set_identity(x),
            },
            {
                "text": "超级管理员",
                "on_release": lambda x="超级管理员": self.set_identity(x),
            }
        ]

        self.identity_menu = MDDropdownMenu(
            caller=caller,
            items=menu_items,
            width=dp(200),
            position="auto",
            max_height=dp(200),
        )
        self.identity_menu.open()

    def set_identity(self, identity):
        """设置选择的身份"""
        try:
            if not identity:
                display_text = "请选择登录身份"
                self.identity = ""
            else:
                display_text = identity
                self.identity = identity

            # 更新显示文本
            if self.login_form and hasattr(self.login_form.ids, 'identity_text'):
                self.login_form.ids.identity_text.text = display_text

            # 关闭菜单
            if self.identity_menu:
                self.identity_menu.dismiss()
                
            logger.info(f"身份选择: {identity} -> 显示: {display_text}")
            
        except Exception as e:
            logger.error(f"设置身份失败: {e}")

    def on_login(self):
        """处理登录逻辑"""
        try:
            # 检查身份选择
            if not self.identity or self.identity == "请选择登录身份":
                self.show_error("请先选择登录身份")
                return

            # 禁用登录按钮，防止重复点击
            login_button = self.login_form.ids.login_button
            login_button.disabled = True
            
            # 显示加载提示
            self.show_info("正在登录，请稍候...")
            
            # 异步处理登录
            Clock.schedule_once(lambda dt: self._async_login(), 0.1)
                
        except Exception as e:
            logger.error(f"登录处理失败: {e}")
            self.show_error(f"登录失败: {str(e)}")
            # 恢复登录按钮
            self._restore_login_button()

    def _async_login(self):
        """异步登录处理"""
        try:
            # 获取当前选项卡的输入内容
            if self.current_tab == "account":
                success = self._handle_account_login()
            else:
                success = self._handle_phone_login()
                
            if success:
                self._process_successful_login()
            else:
                # 登录失败，恢复登录按钮
                self._restore_login_button()
                
        except Exception as e:
            logger.error(f"异步登录处理失败: {e}")
            self.show_error(f"登录失败: {str(e)}")
            self._restore_login_button()

    def _restore_login_button(self):
        """恢复登录按钮状态"""
        try:
            login_button = self.login_form.ids.login_button
            login_button.disabled = False
        except Exception as e:
            logger.error(f"恢复登录按钮状态失败: {e}")

    def _handle_account_login(self):
        """处理账号登录"""
        try:
            # 获取当前选项卡内容
            tab_content = self.login_form.ids.tab_content
            if not tab_content.children:
                return False
                
            login_tab = tab_content.children[0]
            if not isinstance(login_tab, LoginTab):
                return False
                
            username = login_tab.ids.username.text.strip()
            password = login_tab.ids.password.text.strip()
            
            if not username or not password:
                self.show_error("请输入用户名和密码")
                return False
                
            # 检查登录限制
            if not login_limiter.is_allowed(username):
                self.show_error("登录尝试过于频繁，请稍后再试")
                return False
                
            # 执行登录验证
            return self._authenticate_user(username, password)
            
        except Exception as e:
            logger.error(f"账号登录处理失败: {e}")
            return False

    def _handle_phone_login(self):
        """处理手机号登录"""
        try:
            # 获取当前选项卡内容
            tab_content = self.login_form.ids.tab_content
            if not tab_content.children:
                return False
                
            phone_tab = tab_content.children[0]
            if not isinstance(phone_tab, PhoneTab):
                return False
                
            phone = phone_tab.ids.phone.text.strip()
            verification_code = phone_tab.ids.verification_code.text.strip()
            
            if not phone or not verification_code:
                self.show_error("请输入手机号和验证码")
                return False
                
            # 验证手机号格式
            if len(phone) != 11 or not phone.isdigit():
                self.show_error("请输入正确的手机号")
                return False
                
            # 验证验证码格式
            if len(verification_code) != 6 or not verification_code.isdigit():
                self.show_error("请输入6位验证码")
                return False
                
            # 执行手机号登录验证
            return self._authenticate_phone(phone, verification_code)
            
        except Exception as e:
            logger.error(f"手机号登录处理失败: {e}")
            return False

    def _authenticate_user(self, username, password):
        """用户名密码认证"""
        try:
            # 尝试云端认证
            cloud_api = get_cloud_api()
            if cloud_api:
                # 设置较短的超时时间，避免阻塞
                original_timeout = getattr(cloud_api, 'timeout', 30)
                cloud_api.timeout = 10  # 设置10秒超时
                
                try:
                    auth_result = cloud_api.authenticate(username, password)
                    if auth_result and auth_result.get('status') == 'success':
                        login_limiter.record_attempt(username, True)
                        self._setup_user_session(auth_result, username, password)
                        return True
                    else:
                        # 云端认证失败，尝试本地认证
                        logger.error(f"[认证失败        ] 用户名或密码错误")
                        logger.info(f"[云端认证失败，尝试本地认证] {username}")
                        return self._local_authenticate(username, password)
                finally:
                    # 恢复原始超时设置
                    cloud_api.timeout = original_timeout
            else:
                # 本地认证逻辑
                logger.info(f"云端API不可用，使用本地认证: {username}")
                return self._local_authenticate(username, password)
                
        except Exception as e:
            logger.error(f"云端认证异常: {e}，尝试本地认证")
            # 网络异常时，尝试本地认证
            try:
                return self._local_authenticate(username, password)
            except Exception as local_e:
                logger.error(f"本地认证也失败: {local_e}")
                login_limiter.record_attempt(username, False)
                # 根据异常类型提供更友好的错误信息
                if "timeout" in str(e).lower() or "timed out" in str(e).lower():
                    self.show_error("网络连接超时，本地认证也失败")
                elif "connection" in str(e).lower():
                    self.show_error("网络连接失败，本地认证也失败")
                else:
                    self.show_error("认证过程中发生错误")
                return False

    def _authenticate_phone(self, phone, verification_code):
        """手机号验证码认证"""
        try:
            # 尝试云端验证
            cloud_api = get_cloud_api()
            if cloud_api:
                auth_result = cloud_api.verify_phone_code(phone, verification_code, self.identity)
                if auth_result and auth_result.get('success'):
                    self._setup_user_session(auth_result, phone, "")
                    return True
                else:
                    error_msg = auth_result.get('message', '验证失败') if auth_result else '网络连接失败'
                    self.show_error(error_msg)
                    return False
            else:
                # 本地验证逻辑（简化版）
                self.show_error("手机号登录需要网络连接")
                return False
                
        except Exception as e:
            logger.error(f"手机号认证失败: {e}")
            self.show_error("验证过程中发生错误")
            return False

    def _local_authenticate(self, username, password):
        """本地认证逻辑"""
        try:
            # 导入密码验证工具
            try:
                from mobile.utils.password_utils import verify_password
            except ImportError:
                logger.error("无法导入密码验证工具")
                self.show_error("系统错误，请联系管理员")
                return False
            
            # 首先检查本地用户数据文件
            user_data_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'user_data.json')
            
            if os.path.exists(user_data_path):
                with open(user_data_path, 'r', encoding='utf-8') as f:
                    user_data = json.load(f)
                
                # 检查用户名和密码是否匹配
                stored_username = user_data.get('username', '')
                stored_password = user_data.get('password', '')
                
                if username == stored_username:
                    # 使用密码验证工具验证密码
                    if verify_password(password, stored_password):
                        logger.info(f"本地认证成功 (user_data.json): {username}")
                        
                        # 构造认证结果，模拟云端返回格式
                        auth_result = {
                            'success': True,
                            'user_info': {
                                'custom_id': user_data.get('custom_id', user_data.get('user_id', '')),
                                'username': username,
                                'nickname': user_data.get('nickname', username),
                                'avatar': user_data.get('avatar', ''),
                                'phone': user_data.get('phone', ''),
                                'email': user_data.get('email', ''),
                                'identity': user_data.get('identity', self.identity)
                            },
                            'token': user_data.get('token', ''),
                            'message': '本地认证成功'
                        }
                        
                        # 设置用户会话
                        self._setup_user_session(auth_result, username, password)
                        return True
            
            # 检查users.json文件
            users_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'users.json')
            if os.path.exists(users_file):
                with open(users_file, 'r', encoding='utf-8') as f:
                    users_data = json.load(f)
                    
                    for user in users_data:
                        if user.get('username') == username:
                            stored_password = user.get('password', '')
                            
                            # 使用密码验证工具验证密码
                            if verify_password(password, stored_password):
                                logger.info(f"本地认证成功 (users.json): {username}")
                                
                                # 构造认证结果，模拟云端返回格式
                                auth_result = {
                                    'success': True,
                                    'user_info': {
                                        'custom_id': user.get('custom_id', user.get('user_id', '')),
                                        'username': username,
                                        'nickname': user.get('nickname', username),
                                        'avatar': user.get('avatar', ''),
                                        'phone': user.get('phone', ''),
                                        'email': user.get('email', ''),
                                        'identity': user.get('identity', self.identity)
                                    },
                                    'token': user.get('token', ''),
                                    'message': '本地认证成功'
                                }
                                
                                # 设置用户会话
                                self._setup_user_session(auth_result, username, password)
                                return True
                            break  # 找到用户但密码不匹配，跳出循环
            
            logger.warning(f"[本地认证失败      ] 用户名或密码不匹配")
            self.show_error("用户名或密码错误")
            return False
                
        except Exception as e:
            logger.error(f"本地认证失败: {e}")
            self.show_error("本地认证过程中发生错误")
            return False

    def _setup_user_session(self, auth_result, username, password):
        """设置用户会话"""
        try:
            # 获取用户管理器
            from mobile.utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            
            user_id = auth_result.get("user_id")
            custom_id = auth_result.get("custom_id", user_id)

            # 切换到登录用户
            user = user_manager.switch_user(custom_id, force_save=True)

            if not user:
                # 如果本地没有对应用户账户，使用云端返回的信息创建一个新账户
                user_info = auth_result.get('user_info', {})
                user = user_manager.add_account(
                    username=username,
                    password=password,
                    full_name=user_info.get('full_name', username),
                    role=user_info.get('role', self.identity),
                    user_id=user_id,
                    custom_id=custom_id
                )

                if not user:
                    self.show_error("创建用户账户失败")
                    return False

            # 构建用户数据
            user_data = {
                'custom_id': custom_id,
                'user_id': user_id,
                'username': username,
                'full_name': auth_result.get('full_name') or user.get('full_name') or username,
                'role': auth_result.get('role') or user.get('role') or self.identity,
                'login_time': datetime.now().isoformat(),
                'auth_type': 'local' if 'access_token' not in auth_result else 'cloud'
            }
            
            # 设置应用用户数据
            app = MDApp.get_running_app()
            if app:
                app.set_user_data(user_data)
                logger.info(f"已设置应用用户数据: {user_data.get('full_name')} ({user_data.get('custom_id')})")

            # 显示登录成功消息
            full_name = user_data.get('full_name', username)
            self.show_success(f"欢迎回来，{full_name}")

            # 设置cloud_api认证信息
            self._setup_cloud_api(auth_result)
            
            # 保存登录设置（包括凭据和身份信息）
            self._save_login_settings()
            
            return True

        except Exception as e:
            logger.error(f"设置用户会话失败: {e}")
            return False

    def _setup_cloud_api(self, auth_result):
        """设置cloud_api认证信息"""
        try:
            cloud_api = get_cloud_api()
            
            if cloud_api and 'access_token' in auth_result:
                cloud_api.token = auth_result['access_token']
                
                # 保存token到UserStorage
                try:
                    from mobile.utils.storage import UserStorage
                    UserStorage.save_token(auth_result['access_token'])
                except Exception as storage_e:
                    logger.error(f"保存token到UserStorage失败: {str(storage_e)}")
                
                # 设置custom_id
                if 'custom_id' in auth_result:
                    cloud_api.custom_id = auth_result['custom_id']
                
                # 保存认证信息
                cloud_api.save_auth_info()
        except Exception as e:
            logger.error(f"设置cloud_api认证信息时出错: {str(e)}")

    def _process_successful_login(self):
        """处理登录成功后的逻辑"""
        try:
            # 导航到主页
            Clock.schedule_once(lambda dt: self.navigate_to_homepage(), 1)
        except Exception as e:
            logger.error(f"处理登录成功逻辑失败: {e}")

    def navigate_to_homepage(self):
        """导航到主页 - 包含陪诊师角色处理"""
        try:
            # 获取当前用户
            from mobile.utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()

            if not current_user:
                self.show_error("请先登录")
                return

            # 处理上传队列
            self._process_upload_queue_after_login()

            # 根据角色导航到不同页面 - 包含陪诊师
            role = current_user.role
            screen_map = {
                "个人用户": "homepage_screen",
                "健康顾问": "consultant_screen",
                "单位管理员": "unit_screen",
                "超级管理员": "supermanager_screen",
                "陪诊师": "companion_screen"  # 修正拼写错误
            }

            target_screen = screen_map.get(role, 'homepage_screen')
            self._load_and_navigate(target_screen)

        except Exception as e:
            self.show_error(f"导航到主页时出错: {str(e)}")
            logger.error(f"导航到主页时出错: {str(e)}")

    def _process_upload_queue_after_login(self):
        """登录后处理上传队列"""
        try:
            # 这里可以添加处理上传队列的逻辑
            pass
        except Exception as e:
            logger.error(f"处理上传队列失败: {e}")

    def _load_and_navigate(self, target_screen):
        """加载并导航到目标屏幕"""
        try:
            if self.manager:
                self.manager.current = target_screen
        except Exception as e:
            logger.error(f"导航到{target_screen}失败: {e}")
            # 回退到主页
            if self.manager:
                self.manager.current = 'homepage_screen'

    def on_request_verification_code(self):
        """请求验证码"""
        try:
            # 获取手机号
            tab_content = self.login_form.ids.tab_content
            if not tab_content.children:
                return
                
            phone_tab = tab_content.children[0]
            if not isinstance(phone_tab, PhoneTab):
                return
                
            phone = phone_tab.ids.phone.text.strip()
            
            if not phone:
                self.show_error("请输入手机号")
                return
                
            if len(phone) != 11 or not phone.isdigit():
                self.show_error("请输入正确的手机号")
                return
                
            # 发送验证码请求
            cloud_api = get_cloud_api()
            if cloud_api:
                result = cloud_api.send_verification_code(phone)
                if result and result.get('success'):
                    self.show_success("验证码已发送")
                else:
                    error_msg = result.get('message', '发送失败') if result else '网络连接失败'
                    self.show_error(error_msg)
            else:
                self.show_error("需要网络连接发送验证码")
                
        except Exception as e:
            logger.error(f"请求验证码失败: {e}")
            self.show_error("发送验证码时发生错误")

    def on_wechat_login(self):
        """微信登录"""
        self.show_info("微信登录功能开发中")

    def on_fingerprint_login(self):
        """指纹登录"""
        self.show_info("指纹登录功能开发中")

    def on_face_login(self):
        """人脸识别登录"""
        self.show_info("人脸识别登录功能开发中")

    def on_register(self):
        """跳转到注册页面"""
        try:
            if self.manager:
                self.manager.current = 'register'
        except Exception as e:
            logger.error(f"跳转到注册页面失败: {e}")
            self.show_error("跳转失败")

    def load_saved_credentials(self, dt=0):
        """加载已保存的凭据"""
        try:
            if os.path.exists(self.credentials_file):
                with open(self.credentials_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # 恢复身份选择
                saved_identity = data.get('identity', '')
                if saved_identity and self.login_form:
                    self.set_identity(saved_identity)
                    
                # 恢复保留密码设置
                self.remember_password = data.get('remember_password', False)
                if self.login_form:
                    remember_checkbox = self.login_form.ids.get('remember_password_checkbox')
                    if remember_checkbox:
                        remember_checkbox.active = self.remember_password
                
                # 恢复自动登录设置
                self.auto_login = data.get('auto_login', False)
                if self.login_form:
                    auto_login_checkbox = self.login_form.ids.get('auto_login_checkbox')
                    if auto_login_checkbox:
                        auto_login_checkbox.active = self.auto_login
                
                # 如果保留密码，恢复用户名和密码
                if self.remember_password:
                    saved_username = data.get('username', '')
                    saved_password = data.get('password', '')
                    
                    if saved_username and self.login_form:
                        # 获取当前活动的标签页
                        tab_content = self.login_form.ids.tab_content
                        if tab_content and tab_content.children:
                            current_tab = tab_content.children[0]
                            
                            # 根据当前标签页类型填充数据
                            if hasattr(current_tab, 'ids'):
                                if self.current_tab == "account":
                                    # 账号登录标签页
                                    if 'username' in current_tab.ids:
                                        current_tab.ids.username.text = saved_username
                                    if 'password' in current_tab.ids and saved_password:
                                        current_tab.ids.password.text = saved_password
                                elif self.current_tab == "phone":
                                    # 手机号登录标签页
                                    if 'phone' in current_tab.ids and saved_username.isdigit():
                                        current_tab.ids.phone.text = saved_username
                
                # 如果启用自动登录且有保存的凭据，执行自动登录
                if self.auto_login and self.remember_password and not self._auto_login_disabled:
                    saved_username = data.get('username', '')
                    saved_password = data.get('password', '')
                    if saved_username and (saved_password or self.current_tab == "phone"):
                        Clock.schedule_once(lambda dt: self._perform_auto_login(saved_username, saved_password), 1.0)
                    
        except Exception as e:
            logger.error(f"加载保存的凭据失败: {e}")
    
    def _perform_auto_login(self, username, password):
        """执行自动登录"""
        try:
            # 设置用户名和密码到界面
            if self.login_form:
                tab_content = self.login_form.ids.tab_content
                if tab_content and tab_content.children:
                    current_tab = tab_content.children[0]
                    
                    if hasattr(current_tab, 'ids'):
                        if self.current_tab == "account":
                            # 账号登录标签页
                            if 'username' in current_tab.ids:
                                current_tab.ids.username.text = username
                            if 'password' in current_tab.ids and password:
                                current_tab.ids.password.text = password
                        elif self.current_tab == "phone":
                            # 手机号登录标签页
                            if 'phone' in current_tab.ids and username.isdigit():
                                current_tab.ids.phone.text = username
            
            # 执行登录
            self.on_login()
            
        except Exception as e:
            logger.error(f"自动登录失败: {e}")
    
    def on_remember_password_changed(self, active):
        """处理保留密码选项变化"""
        self.remember_password = active
        
        # 如果取消保留密码，也取消自动登录
        if not active and self.auto_login:
            self.auto_login = False
            if self.login_form:
                auto_login_checkbox = self.login_form.ids.get('auto_login_checkbox')
                if auto_login_checkbox:
                    auto_login_checkbox.active = False
        
        # 保存设置
        self._save_login_settings()
    
    def on_auto_login_changed(self, active):
        """处理自动登录选项变化"""
        self.auto_login = active
        
        # 如果启用自动登录，必须先启用保留密码
        if active and not self.remember_password:
            self.remember_password = True
            if self.login_form:
                remember_checkbox = self.login_form.ids.get('remember_password_checkbox')
                if remember_checkbox:
                    remember_checkbox.active = True
        
        # 保存设置
        self._save_login_settings()
    
    def _save_login_settings(self):
        """保存登录设置"""
        try:
            data = {}
            
            # 读取现有数据
            if os.path.exists(self.credentials_file):
                with open(self.credentials_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            
            # 更新设置
            data['remember_password'] = self.remember_password
            data['auto_login'] = self.auto_login
            data['identity'] = self.identity
            
            # 如果保留密码，保存用户名和密码
            if self.remember_password and self.login_form:
                # 获取当前活动的标签页内容
                tab_content = self.login_form.ids.tab_content
                if tab_content and tab_content.children:
                    # 获取第一个子组件（当前显示的标签页）
                    current_tab = tab_content.children[0]
                    
                    # 根据当前标签页类型获取输入内容
                    if hasattr(current_tab, 'ids'):
                        if self.current_tab == "account":
                            # 账号登录标签页
                            if 'username' in current_tab.ids:
                                username = current_tab.ids.username.text.strip()
                                if username:
                                    data['username'] = username
                            if 'password' in current_tab.ids:
                                password = current_tab.ids.password.text.strip()
                                if password:
                                    data['password'] = password
                        elif self.current_tab == "phone":
                            # 手机号登录标签页
                            if 'phone' in current_tab.ids:
                                phone = current_tab.ids.phone.text.strip()
                                if phone:
                                    data['username'] = phone  # 将手机号作为用户名保存
                            # 手机号登录不保存验证码
                            data.pop('password', None)
            else:
                # 如果不保留密码，清除保存的凭据
                data.pop('username', None)
                data.pop('password', None)
            
            # 保存到文件
            os.makedirs(os.path.dirname(self.credentials_file), exist_ok=True)
            with open(self.credentials_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.debug(f"已保存登录设置: remember_password={self.remember_password}, auto_login={self.auto_login}")
                
        except Exception as e:
            logger.error(f"保存登录设置失败: {e}")

    def show_error(self, message):
        """显示错误消息 - 使用BaseScreen的方法"""
        try:
            # 使用BaseScreen的show_snackbar方法
            self.show_snackbar(message, "error")
        except Exception as e:
            logger.error(f"显示错误消息失败: {e}")
            # 回退到直接创建snackbar
            try:
                snackbar = MDSnackbar(
                    MDSnackbarText(text=message),
                    md_bg_color=[0.96, 0.26, 0.21, 1],
                )
                snackbar.open()
            except:
                pass

    def show_success(self, message):
        """显示成功消息 - 使用BaseScreen的方法"""
        try:
            # 使用BaseScreen的show_snackbar方法
            self.show_snackbar(message, "success")
        except Exception as e:
            logger.error(f"显示成功消息失败: {e}")
            # 回退到直接创建snackbar
            try:
                snackbar = MDSnackbar(
                    MDSnackbarText(text=message),
                    md_bg_color=[0.30, 0.69, 0.31, 1],
                )
                snackbar.open()
            except:
                pass

    def show_info(self, message):
        """显示信息消息 - 使用BaseScreen的方法"""
        try:
            # 使用BaseScreen的show_snackbar方法
            self.show_snackbar(message, "info")
        except Exception as e:
            logger.error(f"显示信息消息失败: {e}")
            # 回退到直接创建snackbar
            try:
                snackbar = MDSnackbar(
                    MDSnackbarText(text=message),
                    md_bg_color=[0.133, 0.46, 0.82, 1],
                )
                snackbar.open()
            except:
                pass

    def show_info(self, message):
        """显示提示消息"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
            )
            snackbar.open()
        except Exception as e:
            logger.error(f"显示提示消息失败: {e}")

from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty, ListProperty
from mobile.screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
from kivy.uix.image import Image
from kivy.uix.widget import Widget
import os
import json
import sys
from datetime import datetime
import threading
import tempfile
import traceback
from kivy.logger import Logger

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.card import MDCard
from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
from kivymd.uix.label import MDLabel
from kivymd.uix.list import MDList, MDListItem, MDListItemHeadlineText, MDListItemSupportingText, MDListItemTrailingIcon
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.screen import MDScreen
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.slider import MDSlider
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.textfield import MDTextField

# 导入主题和字体样式
try:
    from mobile.theme import AppTheme, AppMetrics, FontStyles, FontManager
except ImportError:
    # 如果无法从mobile包导入，则尝试直接导入
    try:
        from theme import AppTheme, AppMetrics, FontStyles, FontManager
    except ImportError:
        # 如果两个都失败，抛出异常而不是创建模拟类
        raise ImportError("无法导入主题配置，请检查theme.py文件是否存在且可访问")

# 导入API客户端
try:
    from mobile.api.api_client import APIClient as MobileAPIClient
    APIClient: type = MobileAPIClient  # type: ignore
except ImportError:
    try:
        from api.api_client import APIClient as LocalAPIClient
        APIClient: type = LocalAPIClient  # type: ignore
    except ImportError:
        # 如果API客户端不存在，创建一个占位符
        class APIClient:
            pass

# 定义KV语言字符串
KV = '''
<HealthDataModuleCard>:
    orientation: 'vertical'
    size_hint: None, None
    size: dp(140), dp(110)  # 进一步减小卡片大小
    md_bg_color: app.theme.DATA_CARD_BACKGROUND if app and hasattr(app, 'theme') and app.theme is not None else [0.95, 0.95, 0.95, 1]
    radius: [dp(10)]
    elevation: 3
    padding: [dp(8), dp(8), dp(8), dp(8)]  # 调整内边距
    pos_hint: {'center_x': 0.5}
    ripple_behavior: True

    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(4)  # 减小间距

        MDIconButton:
            icon: root.icon
            font_size: dp(24)  # 减小图标大小
            pos_hint: {'center_x': 0.5}
            theme_icon_color: "Custom"
            icon_color: root.icon_color if root.icon_color else (app.theme.PRIMARY_DARK if app and hasattr(app, 'theme') and app.theme is not None else [0.2, 0.6, 1, 1])
            disabled: True

        MDLabel:
            text: root.title
            halign: 'center'
            font_style: "Body"
            role: "medium"
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_PRIMARY if app and hasattr(app, 'theme') and app.theme is not None else [0, 0, 0, 1]
            bold: True
            size_hint_y: None
            height: self.texture_size[1]
            font_size: dp(14)  # 减小字体大小

        MDLabel:
            text: root.description
            halign: 'center'
            font_style: "Label"
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_SECONDARY if app and hasattr(app, 'theme') and app.theme is not None else [0.5, 0.5, 0.5, 1]
            size_hint_y: None
            height: self.texture_size[1]
            shorten: True
            shorten_from: 'right'
            font_size: dp(12)  # 减小字体大小

<QuickActionCard>:
    orientation: 'horizontal'
    size_hint: 1, None
    height: dp(60)
    md_bg_color: app.theme.CARD_BACKGROUND if app and hasattr(app, 'theme') and app.theme is not None else [0.9, 0.9, 0.9, 1]
    radius: [dp(8)]
    elevation: 2
    padding: [dp(12), dp(8), dp(12), dp(8)]
    ripple_behavior: True

    MDIconButton:
        icon: root.icon
        font_size: dp(24)
        theme_icon_color: "Custom"
        icon_color: root.icon_color if root.icon_color else (app.theme.PRIMARY_DARK if app and hasattr(app, 'theme') and app.theme is not None else [0.2, 0.6, 1, 1])
        size_hint_x: None
        width: dp(40)
        disabled: True

    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(2)

        MDLabel:
            text: root.title
            font_style: "Body"
            role: "medium"
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_PRIMARY if app and hasattr(app, 'theme') and app.theme is not None else [0, 0, 0, 1]
            bold: True
            size_hint_y: None
            height: self.texture_size[1]

        MDLabel:
            text: root.subtitle
            font_style: "Label"
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_SECONDARY if app and hasattr(app, 'theme') and app.theme is not None else [0.5, 0.5, 0.5, 1]
            size_hint_y: None
            height: self.texture_size[1]

    MDIconButton:
        icon: "chevron-right"
        font_size: dp(20)
        theme_icon_color: "Custom"
        icon_color: app.theme.TEXT_SECONDARY if app and hasattr(app, 'theme') and app.theme is not None else [0.5, 0.5, 0.5, 1]
        size_hint_x: None
        width: dp(32)
        disabled: True

<HealthDataManagementScreen>:
'''
# 只加载一次KV，确保ids绑定唯一
Builder.load_string(KV)

class HealthDataModuleCard(MDCard):
    """健康资料管理模块卡片组件"""
    icon = StringProperty("heart-pulse")
    title = StringProperty("功能")
    description = StringProperty("")
    bg_color = ListProperty(None)
    icon_color = ListProperty(None)
    on_release = ObjectProperty(None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 初始化时确保on_release有一个默认值
        if self.on_release is None:
            self.on_release = self._default_on_release
        # 绑定MDCard的on_release事件
        self.bind(on_release=self._on_release)

    def _default_on_release(self, *args):
        """默认的on_release处理函数"""
        Logger.info(f"[HealthDataModuleCard] 默认on_release处理函数被调用: {self.title}")

    def _on_release(self, instance):
        """处理MDCard的on_release事件"""
        # 确保on_release不为None
        if self.on_release is None:
            self.on_release = self._default_on_release
            
        # 更安全的事件处理方式
        if callable(self.on_release):
            try:
                self.on_release()
            except Exception as e:
                Logger.error(f"[HealthDataModuleCard] on_release 调用失败: {e}")
        else:
            Logger.warning(f"[HealthDataModuleCard] on_release 事件处理函数不可用: {self.title}")

class QuickActionCard(MDCard):
    """快速操作卡片组件"""
    icon = StringProperty("heart-pulse")
    title = StringProperty("操作")
    subtitle = StringProperty("")
    icon_color = ListProperty(None)
    on_release = ObjectProperty(None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 初始化时确保on_release有一个默认值
        if self.on_release is None:
            self.on_release = self._default_on_release
        # 绑定MDCard的on_release事件
        self.bind(on_release=self._on_release)

    def _default_on_release(self, *args):
        """默认的on_release处理函数"""
        Logger.info(f"[QuickActionCard] 默认on_release处理函数被调用: {self.title}")

    def _on_release(self, instance):
        """处理MDCard的on_release事件"""
        # 确保on_release不为None
        if self.on_release is None:
            self.on_release = self._default_on_release
            
        # 更安全的事件处理方式
        if callable(self.on_release):
            try:
                self.on_release()
            except Exception as e:
                Logger.error(f"[QuickActionCard] on_release 调用失败: {e}")
        else:
            Logger.warning(f"[QuickActionCard] on_release 事件处理函数不可用: {self.title}")

class HealthDataManagementScreen(BaseScreen):
    """健康资料管理屏幕"""
    
    def __init__(self, **kwargs):
        # 设置导航栏属性
        kwargs['screen_title'] = '健康资料管理'
        kwargs['show_top_bar'] = True  # 显示顶端导航栏
        kwargs['top_bar_action_icon'] = 'refresh'
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        # 添加初始化状态标记
        self._ui_initialized = False
        self._content_setup = False
    
    def on_enter(self, *args):
        """进入屏幕时调用"""
        # 调用父类方法确保基础结构已初始化
        super().on_enter(*args)
        
        # 强制校验登录状态
        app = MDApp.get_running_app()

        # 检查是否已登录
        is_logged_in = False
        user_data = getattr(app, 'user_data', None)
        
        # 首先检查app.user_data是否存在且有效
        if user_data is not None and user_data.get('custom_id'):
            # 对于本地认证用户，直接认为已登录
            auth_type = user_data.get('auth_type', 'cloud')
            if auth_type == 'local':
                is_logged_in = True
                Logger.info(f"[HealthDataManagementScreen] 本地认证用户已登录: {user_data.get('full_name')}")
            else:
                # 对于云端认证用户，检查cloud_api的认证状态
                try:
                    from utils.cloud_api import get_cloud_api
                    cloud_api = get_cloud_api()
                    if cloud_api and cloud_api.is_authenticated():
                        is_logged_in = True
                        Logger.info(f"[HealthDataManagementScreen] 云端认证用户已登录: {user_data.get('full_name')}")
                    else:
                        Logger.info(f"[HealthDataManagementScreen] 云端认证用户需要重新认证")
                        
                except Exception as e:
                    Logger.error(f"[HealthDataManagementScreen] 检查认证状态时出错: {e}")
                    # 如果检查认证状态出错，但有user_data，仍然允许继续使用
                    is_logged_in = True
                    Logger.info(f"[HealthDataManagementScreen] 认证检查出错但用户数据有效，允许继续使用")
        else:
            Logger.info("[HealthDataManagementScreen] 未找到有效的用户数据")

        if not is_logged_in:
            # 未登录或认证无效，强制跳转回登录页并提示
            Logger.warning("[HealthDataManagementScreen] 用户未登录或认证无效，跳转到登录页")
            if self.manager:
                self.manager.current = 'login_screen'
            # 显示提示信息
            snackbar = MDSnackbar(MDSnackbarText(text="请先登录"))
            snackbar.open()
            return

        Logger.info("[HealthDataManagementScreen] 用户认证通过，开始加载页面内容")
        # 延迟加载数据以确保UI已完全初始化
        Clock.schedule_once(lambda dt: self.load_health_data_modules(), 0.1)
        Clock.schedule_once(lambda dt: self.load_quick_actions(), 0.2)
    
    def init_ui(self, dt=0):
        """初始化UI"""
        # 健康资料管理屏幕显示顶端导航栏
        self.show_top_bar = True
        # 调用父类的init_ui方法（会统一添加Logo并清理重复）
        super().init_ui(dt)
        
        # 标记UI已初始化
        self._ui_initialized = True
        Logger.info("[HealthDataManagementScreen] 成功设置屏幕内容")
    
    def do_content_setup(self):
        """在content_container中添加内容"""
        try:
            # 安全地获取content_container
            content_container = None
            if hasattr(self, 'ids') and isinstance(self.ids, dict):
                content_container = self.ids.get('content_container')
            
            if not content_container:
                Logger.error(f"[HealthDataManagementScreen] ERROR: 无法找到content_container")
                return
            
            # 清空content_container中的现有内容
            content_container.clear_widgets()
            
            # 创建欢迎区域
            from kivymd.uix.card import MDCard
            from kivymd.uix.label import MDLabel
            from kivymd.uix.gridlayout import MDGridLayout
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.button import MDIconButton
            
            # 主内容区域 - 直接添加到content_container，不再使用ScrollView
            main_layout = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                height=int(self.height if self.height > 0 else dp(800)),  # 初始高度
                padding=[int(dp(16)), int(dp(16)), int(dp(16)), int(dp(100))],  # 减小左右内边距
                spacing=int(dp(20))  # 稍微减小间距
            )
            main_layout.bind(minimum_height=main_layout.setter('height'))
            
            # 欢迎区域
            welcome_card = MDCard(
                orientation='vertical',
                size_hint_y=None,
                padding=[int(dp(20)), int(dp(16)), int(dp(20)), int(dp(16))],
                spacing=int(dp(8)),
                radius=[int(dp(16))],
                elevation=2,
                md_bg_color=getattr(self.app.theme, 'CARD_BACKGROUND', [0.95, 0.95, 0.95, 1]) if self.app and hasattr(self.app, 'theme') else [0.95, 0.95, 0.95, 1]
            )
            # 绑定卡片高度到其最小高度
            welcome_card.bind(minimum_height=welcome_card.setter('height'))
            
            welcome_layout = MDBoxLayout(
                orientation='vertical',
                spacing=int(dp(4))
            )
            
            welcome_label = MDLabel(
                text="健康资料管理",
                font_style="Body",
                role="large",
                bold=True,
                halign="left",
                size_hint_y=None,
                height=int(dp(30)),
                theme_text_color="Custom",
                text_color=getattr(self.app.theme, 'TEXT_PRIMARY', [0, 0, 0, 1]) if self.app and hasattr(self.app, 'theme') else [0, 0, 0, 1]
            )
            # 保存welcome_label的引用，以便后续可以更新
            self.welcome_label = welcome_label
            
            welcome_layout.add_widget(welcome_label)
            welcome_card.add_widget(welcome_layout)
            main_layout.add_widget(welcome_card)
            
            # 健康资料管理功能模块区域 (MDCard)
            self.health_modules_card = MDCard(
                orientation='vertical',
                size_hint_y=None,
                padding=[int(dp(16)), int(dp(16)), int(dp(16)), int(dp(16))],  # 减小内边距
                spacing=int(dp(12)),  # 减小间距
                radius=[int(dp(16))],
                elevation=2,
                md_bg_color=getattr(self.app.theme, 'CARD_BACKGROUND', [0.95, 0.95, 0.95, 1]) if self.app and hasattr(self.app, 'theme') else [0.95, 0.95, 0.95, 1]
            )
            # 绑定卡片高度到其最小高度
            self.health_modules_card.bind(minimum_height=self.health_modules_card.setter('height'))
            
            # 健康资料管理标题
            module_title = MDLabel(
                text="健康资料管理功能",
                font_style="Body",
                role="large",
                bold=True,
                halign="left",
                size_hint_y=None,
                height=int(dp(30)),
                theme_text_color="Custom",
                text_color=getattr(self.app.theme, 'PRIMARY_DARK', [0.2, 0.6, 1, 1]) if self.app and hasattr(self.app, 'theme') else [0.2, 0.6, 1, 1]
            )
            self.health_modules_card.add_widget(module_title)
            
            # 模块网格布局
            modules_grid = MDGridLayout(
                cols=2,
                size_hint_y=None,
                spacing=int(dp(12))  # 稍微减小间距
            )
            # 保存modules_grid的引用
            self.modules_grid = modules_grid
            self.health_modules_card.add_widget(modules_grid)
            
            # 绑定网格高度到其最小高度，确保正确计算
            modules_grid.bind(minimum_height=modules_grid.setter('height'))
            
            main_layout.add_widget(self.health_modules_card)
            
            # 快速操作区域 (MDCard)
            self.quick_actions_card = MDCard(
                orientation='vertical',
                size_hint_y=None,
                padding=[int(dp(20)), int(dp(16)), int(dp(20)), int(dp(20))],
                spacing=int(dp(16)),
                radius=[int(dp(16))],
                elevation=2,
                md_bg_color=getattr(self.app.theme, 'CARD_BACKGROUND', [0.95, 0.95, 0.95, 1]) if self.app and hasattr(self.app, 'theme') else [0.95, 0.95, 0.95, 1]
            )
            # 绑定卡片高度到其最小高度
            self.quick_actions_card.bind(minimum_height=self.quick_actions_card.setter('height'))
            
            # 快速操作标题栏，包含增加和删除按钮
            quick_actions_header = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=int(dp(40)),
                spacing=int(dp(8))
            )
            
            quick_actions_title = MDLabel(
                text="快速操作",
                font_style="Body",
                role="large",
                bold=True,
                halign="left",
                size_hint_x=1,
                size_hint_y=None,
                height=int(dp(30)),
                theme_text_color="Custom",
                text_color=getattr(self.app.theme, 'PRIMARY_DARK', [0.2, 0.6, 1, 1]) if self.app and hasattr(self.app, 'theme') else [0.2, 0.6, 1, 1]
            )
            
            # 添加按钮
            add_button = MDIconButton(
                icon="plus",
                font_size=int(dp(24)),
                theme_icon_color="Custom",
                icon_color=getattr(self.app.theme, 'SUCCESS_COLOR', [0, 1, 0, 1]) if self.app and hasattr(self.app, 'theme') else [0, 1, 0, 1],
                size_hint_x=None,
                width=int(dp(40))
            )
            # 使用更安全的方式绑定事件
            def _on_add_quick_action(instance):
                if hasattr(self, 'on_add_quick_action') and self.on_add_quick_action is not None and callable(self.on_add_quick_action):
                    self.on_add_quick_action()
                else:
                    Logger.warning("[HealthDataManagementScreen] on_add_quick_action 方法不可用")
            
            add_button.bind(on_release=_on_add_quick_action)
            
            # 删除按钮
            remove_button = MDIconButton(
                icon="minus",
                font_size=int(dp(24)),
                theme_icon_color="Custom",
                icon_color=getattr(self.app.theme, 'ERROR_COLOR', [1, 0, 0, 1]) if self.app and hasattr(self.app, 'theme') else [1, 0, 0, 1],
                size_hint_x=None,
                width=int(dp(40))
            )
            # 使用更安全的方式绑定事件
            def _on_remove_quick_action(instance):
                if hasattr(self, 'on_remove_quick_action') and self.on_remove_quick_action is not None and callable(self.on_remove_quick_action):
                    self.on_remove_quick_action()
                else:
                    Logger.warning("[HealthDataManagementScreen] on_remove_quick_action 方法不可用")
            
            remove_button.bind(on_release=_on_remove_quick_action)
            
            quick_actions_header.add_widget(quick_actions_title)
            quick_actions_header.add_widget(add_button)
            quick_actions_header.add_widget(remove_button)
            self.quick_actions_card.add_widget(quick_actions_header)
            
            # 快速操作内容
            quick_actions_box = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                spacing=int(dp(12))
            )
            # 保存quick_actions_box的引用
            self.quick_actions_box = quick_actions_box
            self.quick_actions_card.add_widget(quick_actions_box)
            
            # 绑定快速操作内容区域高度到其最小高度，确保自适应内容
            quick_actions_box.bind(minimum_height=quick_actions_box.setter('height'))
            main_layout.add_widget(self.quick_actions_card)
            
            # 直接将main_layout添加到content_container，不再使用ScrollView
            content_container.add_widget(main_layout)
            
            # 加载模块数据
            self.load_health_data_modules()
            self.load_quick_actions()
            
            Logger.info("[HealthDataManagementScreen] 成功添加内容到content_container")
        except Exception as e:
            Logger.error(f"[HealthDataManagementScreen] 添加内容到content_container失败: {e}")
            import traceback
            traceback.print_exc()
    
    def load_health_data_modules(self):
        """加载健康资料管理模块"""
        try:
            # 确保app实例存在
            if not self.app:
                self.app = MDApp.get_running_app()
                
            # 健康资料管理模块数据
            modules_data = [
                {
                    'title': '健康状态总览',
                    'icon': 'chart-line',
                    'description': '查看自动总结的健康信息',
                    'color': getattr(self.app.theme, 'HEALTH_DATA_COLOR', [0.3, 0.8, 0.3, 1]) if self.app and hasattr(self.app, 'theme') else [0.3, 0.8, 0.3, 1],
                    'action': self.navigate_to_health_overview
                },
                {
                    'title': '基本健康信息',
                    'icon': 'clipboard-text',
                    'description': '录入和修改基本健康信息',
                    'color': getattr(self.app.theme, 'INFO_COLOR', [0, 0, 0, 1]) if self.app and hasattr(self.app, 'theme') else [0, 0, 0, 1],
                    'action': self.navigate_to_basic_health_info
                },
                {
                    'title': '健康资料传阅',
                    'icon': 'file-upload',
                    'description': '上传和管理健康资料',
                    'color': getattr(self.app.theme, 'SUCCESS_COLOR', [0, 0, 0, 1]) if self.app and hasattr(self.app, 'theme') else [0, 0, 0, 1],
                    'action': self.navigate_to_health_file_upload
                },
                {
                    'title': '调查问卷/评估量表',
                    'icon': 'clipboard-list',
                    'description': '填写和查看问卷量表',
                    'color': getattr(self.app.theme, 'WARNING_COLOR', [0, 0, 0, 1]) if self.app and hasattr(self.app, 'theme') else [0, 0, 0, 1],
                    'action': self.navigate_to_questionnaire
                },
                {
                    'title': '用药记录',
                    'icon': 'pill',
                    'description': '管理用药信息和记录',
                    'color': getattr(self.app.theme, 'HEALTH_PURPLE', [0, 0, 0, 1]) if self.app and hasattr(self.app, 'theme') else [0, 0, 0, 1],
                    'action': self.navigate_to_medication_record
                },
                {
                    'title': '健康日记',
                    'icon': 'book-open-variant',
                    'description': '记录日常健康状况',
                    'color': getattr(self.app.theme, 'HEALTH_GREEN', [0, 0, 0, 1]) if self.app and hasattr(self.app, 'theme') else [0, 0, 0, 1],
                    'action': self.navigate_to_health_diary
                },
                {
                    'title': '其它记录',
                    'icon': 'note-text',
                    'description': '其他健康相关记录',
                    'color': getattr(self.app.theme, 'TEXT_SECONDARY', [0, 0, 0, 1]) if self.app and hasattr(self.app, 'theme') else [0, 0, 0, 1],
                    'action': self.navigate_to_other_records
                },
                {
                    'title': '添加功能',
                    'icon': 'plus',
                    'description': '自定义添加更多功能',
                    'color': getattr(self.app.theme, 'PRIMARY_COLOR', [0, 0, 0, 1]) if self.app and hasattr(self.app, 'theme') else [0, 0, 0, 1],
                    'action': self.show_add_feature_dialog
                }
            ]
            
            # 使用实例变量而不是ids字典
            modules_grid = getattr(self, 'modules_grid', None)
            if modules_grid:
                modules_grid.clear_widgets()

                for module in modules_data:
                    # 确保action是一个可调用的函数
                    action = module['action']
                    if action is not None and callable(action):
                        card = HealthDataModuleCard(
                            title=module['title'],
                            icon=module['icon'],
                            description=module['description'],
                            icon_color=module['color'],
                            on_release=action
                        )
                    else:
                        # 如果action不可用，使用一个空的回调函数
                        def empty_callback(*args):
                            Logger.warning(f"[HealthDataManagementScreen] 模块 {module['title']} 的回调函数不可用")
                        
                        card = HealthDataModuleCard(
                            title=module['title'],
                            icon=module['icon'],
                            description=module['description'],
                            icon_color=module['color'],
                            on_release=empty_callback
                        )
                    modules_grid.add_widget(card)
                
                # 确保绑定minimum_height以支持自适应内容
                modules_grid.bind(minimum_height=modules_grid.setter('height'))
                
                # 调度一次更新以确保高度正确计算
                Clock.schedule_once(lambda dt: self._update_modules_height(), 0)
            
            Logger.info("[HealthDataManagementScreen] 成功加载健康资料管理模块")
            
        except Exception as e:
            Logger.error(f"[HealthDataManagementScreen] 加载健康资料管理模块失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _update_modules_height(self):
        """更新模块区域高度"""
        try:
            modules_grid = getattr(self, 'modules_grid', None)
            health_modules_card = getattr(self, 'health_modules_card', None)
            
            if modules_grid and health_modules_card:
                # 强制重新计算网格的最小高度
                modules_grid.do_layout()
                
                # 确保网格有正确的子组件数量
                children_count = len(modules_grid.children)
                rows = max(1, (children_count + 1) // 2)  # 每行2个卡片，计算需要的行数
                
                # 更新网格高度 - 基于实际的行数计算，使用新的卡片高度120dp
                calculated_height = int(dp(120) * rows + dp(16) * max(0, rows - 1))
                modules_grid.height = calculated_height
                
                # 更新健康管理服务卡片高度
                # 计算卡片总高度：标题高度+内边距+网格高度
                title_height = int(dp(30))
                padding_height = int(dp(16) * 2)  # 上下内边距
                grid_height = int(modules_grid.height)
                total_height = int(title_height + padding_height + grid_height)
                
                health_modules_card.height = total_height
                # 确保绑定minimum_height以支持自适应内容
                health_modules_card.bind(minimum_height=health_modules_card.setter('height'))
                
                Logger.info(f"[HealthDataManagementScreen] 更新模块区域高度: 子组件数={children_count}, 行数={rows}, 网格高度={grid_height}, 总高度={total_height}")
        except Exception as e:
            Logger.error(f"[HealthDataManagementScreen] 更新模块区域高度失败: {e}")
            import traceback
            traceback.print_exc()
    
    def go_back(self):
        """返回上一级页面"""
        if self.manager:
            self.manager.current = 'homepage_screen'
    
    def refresh_data(self):
        """刷新数据"""
        # 显示刷新提示
        snackbar = MDSnackbar(MDSnackbarText(text="正在刷新数据..."))
        snackbar.open()
        
        # 重新加载模块
        self.load_health_data_modules()
        
        # 延迟显示完成提示
        Clock.schedule_once(lambda dt: self.show_refresh_complete(), 1)
    
    def show_refresh_complete(self):
        """显示刷新完成提示"""
        snackbar = MDSnackbar(MDSnackbarText(text="数据刷新完成"))
        snackbar.open()
    
    # 导航方法
    def navigate_to_health_overview(self, *args):
        """导航到健康状态总览"""
        try:
            self.manager.current = 'health_overview_screen'
        except Exception as e:
            snackbar = MDSnackbar(MDSnackbarText(text=f"导航失败: {str(e)}"))
            snackbar.open()

    def navigate_to_basic_health_info(self, *args):
        """导航到基本健康信息"""
        try:
            self.manager.current = 'basic_health_info_screen'
        except Exception as e:
            snackbar = MDSnackbar(MDSnackbarText(text=f"导航失败: {str(e)}"))
            snackbar.open()

    def navigate_to_health_file_upload(self, *args):
        """导航到健康资料传阅"""
        try:
            self.manager.current = 'health_document_screen'
        except Exception as e:
            snackbar = MDSnackbar(MDSnackbarText(text=f"导航失败: {str(e)}"))
            snackbar.open()

    def navigate_to_questionnaire(self, *args):
        """导航到调查问卷/评估量表"""
        try:
            self.manager.current = 'survey_screen'
        except Exception as e:
            snackbar = MDSnackbar(MDSnackbarText(text=f"导航失败: {str(e)}"))
            snackbar.open()

    def navigate_to_medication_record(self, *args):
        """导航到用药记录"""
        try:
            self.manager.current = 'medication_management_screen'
        except Exception as e:
            snackbar = MDSnackbar(MDSnackbarText(text=f"导航失败: {str(e)}"))
            snackbar.open()
    
    def navigate_to_health_diary(self, *args):
        """导航到健康日记"""
        try:
            self.manager.current = 'health_diary_screen'
        except Exception as e:
            snackbar = MDSnackbar(MDSnackbarText(text=f"导航失败: {str(e)}"))
            snackbar.open()
    
    def navigate_to_other_records(self, *args):
        """导航到其它记录"""
        try:
            self.manager.current = 'other_records_screen'
        except Exception as e:
            snackbar = MDSnackbar(MDSnackbarText(text=f"导航失败: {str(e)}"))
            snackbar.open()
    
    def show_feature_not_available(self, feature_name, *args):
        """显示功能不可用提示"""
        snackbar = MDSnackbar(MDSnackbarText(text=f"{feature_name}功能暂不可用"))
        snackbar.open()

    def show_feature_coming_soon(self, feature_name, *args):
        """显示功能即将推出提示"""
        snackbar = MDSnackbar(MDSnackbarText(text=f"{feature_name}功能即将推出，敬请期待！"))
        snackbar.open()
    
    def load_quick_actions(self):
        """加载快速操作"""
        quick_actions_data = [
            {
                'title': '健康状态总览',
                'icon': 'chart-line',
                'action': self.navigate_to_health_overview
            },
            {
                'title': '健康日记',
                'icon': 'book-open-variant',
                'action': self.navigate_to_health_diary
            }
        ]

        # 使用实例变量而不是ids字典
        quick_actions_box = getattr(self, 'quick_actions_box', None)
        if quick_actions_box:
            quick_actions_box.clear_widgets()

            for action in quick_actions_data:
                # 确保action是一个可调用的函数
                action_func = action['action']
                if action_func is not None and callable(action_func):
                    card = QuickActionCard(
                        title=action['title'],
                        icon=action['icon'],
                        subtitle="",  # 添加空的subtitle属性
                        on_release=action_func
                    )
                else:
                    # 如果action不可用，使用一个空的回调函数
                    def empty_callback(*args):
                        Logger.warning(f"[HealthDataManagementScreen] 快速操作 {action['title']} 的回调函数不可用")
                    
                    card = QuickActionCard(
                        title=action['title'],
                        icon=action['icon'],
                        subtitle="",  # 添加空的subtitle属性
                        on_release=empty_callback
                    )
                quick_actions_box.add_widget(card)
            
            # 更新快速操作区域高度 - 紧凑布局
            # 使用调度机制确保高度计算在下一帧完成
            Clock.schedule_once(lambda dt: self._update_quick_actions_height(), 0)

    def _update_quick_actions_height(self):
        """更新快速操作区域高度"""
        try:
            # 更新快速操作内容区域高度
            quick_actions_box = getattr(self, 'quick_actions_box', None)
            quick_actions_card = getattr(self, 'quick_actions_card', None)
            
            if quick_actions_box:
                # 确保高度正确计算 - 每个QuickActionCard高度为60dp
                item_height = int(dp(60))
                spacing = int(dp(12))
                content_height = int(len(quick_actions_box.children) * item_height + max(0, (len(quick_actions_box.children) - 1) * spacing))
                quick_actions_box.height = int(content_height or dp(60))
                
            if quick_actions_card and quick_actions_box:
                # 更新快速操作卡片高度，确保包含内容区域和内边距
                content_height = int(quick_actions_box.height if quick_actions_box else dp(60))
                # 计算总高度：标题栏高度 + 内容区域高度 + 上下内边距
                header_height = int(dp(40))  # 标题栏高度
                padding_height = int(dp(36))  # 上下内边距 (16+20)
                total_height = int(header_height + content_height + padding_height)
                
                # 确保最小高度
                min_height = int(dp(120))  # 最小高度
                quick_actions_card.height = int(max(total_height, min_height))
                
                Logger.info(f"[HealthDataManagementScreen] 更新快速操作区域高度: 内容高度={content_height}, 总高度={total_height}")
        except Exception as e:
            Logger.error(f"[HealthDataManagementScreen] 更新快速操作区域高度失败: {e}")
            # 出错时设置默认高度
            quick_actions_card = getattr(self, 'quick_actions_card', None)
            if quick_actions_card:
                quick_actions_card.height = int(dp(200))

    def on_add_quick_action(self):
        """处理添加快速操作按钮点击事件"""
        self.show_info("添加快速操作功能")
        # TODO: 实现添加快速操作的逻辑

    def on_remove_quick_action(self):
        """处理删除快速操作按钮点击事件"""
        self.show_info("删除快速操作功能")
        # TODO: 实现删除快速操作的逻辑

    def show_info(self, message):
        """显示信息提示"""
        # 使用应用程序的通知机制
        app = MDApp.get_running_app()
        if app and hasattr(app, 'show_notification'):
            app.show_notification(message)
        else:
            # 使用Snackbar作为备选
            snackbar = MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                pos_hint={"center_x": 0.5},
                duration=2,
            )
            snackbar.open()
    
    def show_add_feature_dialog(self):
        """显示添加功能对话框"""
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.button import MDButton, MDButtonText
        
        dialog = MDDialog(
            MDDialogHeadlineText(text="添加自定义功能"),
            MDDialogSupportingText(text="此功能正在开发中，将支持用户自定义添加健康管理功能模块。"),
            MDDialogButtonContainer(
                Widget(),
                MDButton(
                    MDButtonText(text="确定"),
                    style="text",
                    on_release=lambda x: dialog.dismiss()
                ),
                spacing="8dp",
            ),
        )
        dialog.open()
    
    def on_back(self):
        """处理返回按钮点击事件"""
        print(f"[HealthDataManagementScreen] 返回按钮被点击")
        try:
            if self.manager:
                print("[HealthDataManagementScreen] 使用manager返回主页")
                self.manager.current = 'homepage_screen'
            else:
                print("[HealthDataManagementScreen] 没有manager，使用应用根节点返回主页")
                app = MDApp.get_running_app()
                if app and hasattr(app, 'root'):
                    app.root.current = 'homepage_screen'
        except Exception as e:
            print(f"[HealthDataManagementScreen] 返回处理错误: {e}")
            import traceback
            traceback.print_exc()

    def on_action(self):
        """处理操作按钮点击事件"""
        print(f"[HealthDataManagementScreen] 操作按钮被点击")
        try:
            self.refresh_data()
        except Exception as e:
            print(f"[HealthDataManagementScreen] 操作处理错误: {e}")
            import traceback
            traceback.print_exc()

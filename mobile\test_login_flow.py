#!/usr/bin/env python3
"""
测试完整的登录流程
"""

import sys
import os
import logging

# 添加移动端路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='[%(levelname)s] %(message)s')

def test_cloud_authentication():
    """测试云端认证"""
    
    print("=" * 50)
    print("测试云端认证流程")
    print("=" * 50)
    
    try:
        from utils.cloud_api import get_cloud_api
        
        cloud_api = get_cloud_api()
        if not cloud_api:
            print("✗ 无法获取云端API实例")
            return False
            
        print(f"✓ 云端API实例获取成功")
        print(f"  基础URL: {cloud_api.base_url}")
        
        # 测试认证
        username = "markey"
        password = "markey0308@163"
        
        print(f"\n开始认证...")
        print(f"  用户名: {username}")
        print(f"  密码: {password}")
        
        auth_result = cloud_api.authenticate(username, password)
        
        if auth_result:
            print(f"✓ 认证结果: {auth_result}")
            if auth_result.get('status') == 'success':
                print("✓ 云端认证成功")
                return True
            else:
                print(f"✗ 云端认证失败: {auth_result.get('message', '未知错误')}")
                return False
        else:
            print("✗ 云端认证返回None")
            return False
            
    except Exception as e:
        print(f"✗ 云端认证异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_local_authentication():
    """测试本地认证"""
    
    print("\n" + "=" * 50)
    print("测试本地认证流程")
    print("=" * 50)
    
    try:
        from utils.password_utils import verify_password
        
        # 从本地数据文件读取用户信息
        import json
        
        users_file = os.path.join(os.path.dirname(__file__), 'data', 'users.json')
        if os.path.exists(users_file):
            with open(users_file, 'r', encoding='utf-8') as f:
                users_data = json.load(f)
                
            for user in users_data:
                if user.get('username') == 'markey':
                    stored_password = user.get('password', '')
                    test_password = "markey0308@163"
                    
                    print(f"找到用户: {user.get('username')}")
                    print(f"存储的密码哈希: {stored_password[:50]}...")
                    print(f"测试密码: {test_password}")
                    
                    result = verify_password(test_password, stored_password)
                    print(f"本地认证结果: {'✓ 成功' if result else '✗ 失败'}")
                    return result
                    
        print("✗ 未找到markey用户")
        return False
        
    except Exception as e:
        print(f"✗ 本地认证异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试登录流程...")
    
    # 测试云端认证
    cloud_success = test_cloud_authentication()
    
    # 测试本地认证
    local_success = test_local_authentication()
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"云端认证: {'✓ 成功' if cloud_success else '✗ 失败'}")
    print(f"本地认证: {'✓ 成功' if local_success else '✗ 失败'}")
    
    if cloud_success:
        print("\n建议: 云端认证正常，检查移动端登录逻辑")
    elif local_success:
        print("\n建议: 本地认证正常，检查云端服务器连接")
    else:
        print("\n建议: 两种认证都失败，检查密码和用户数据")

if __name__ == "__main__":
    main()

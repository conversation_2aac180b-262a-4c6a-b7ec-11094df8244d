import os
import json
import time
import logging
import threading
from threading import Lock
from typing import Dict, Optional, Any

import requests
import mimetypes

from .cloud_api_constants import *
from .cloud_api_helpers import Re<PERSON><PERSON><PERSON><PERSON>, Response<PERSON><PERSON><PERSON>, ServerManager, DataProcessor
from .connection_pool_manager import get_connection_pool_manager

# 配置日志 - 使用简单的日志配置
logger = logging.getLogger(__name__)

# 缓存实例
_cloud_api_instance = None

def get_cloud_api(base_url=None, backup_url=None, auto_switch=True, reset_servers=False):
    """获取CloudAPI实例，单例模式

    Args:
        base_url: 基础URL，可选
        backup_url: 备用URL，可选
        auto_switch: 是否在公网服务器失败时自动切换到本地服务器，默认True
        reset_servers: 是否重置服务器状态，重新从本地服务器开始尝试连接，默认False

    Returns:
        CloudAPI: CloudAPI实例
    """
    global _cloud_api_instance

    if _cloud_api_instance is None:
        _cloud_api_instance = CloudAPI(base_url=base_url, auto_switch=auto_switch)

        # 如果提供了备用URL，设置它
        if backup_url:
            _cloud_api_instance.backup_url = backup_url
    else:
        # 如果需要重置服务器状态
        if reset_servers:
            _cloud_api_instance.reset_server_status()
            
        # 如果提供了新的base_url，则更新
        if base_url:
            _cloud_api_instance.base_url = base_url
            # 如果提供了备用URL，更新它
            if backup_url:
                _cloud_api_instance.backup_url = backup_url

            # 更新自动切换设置
            _cloud_api_instance.auto_switch = auto_switch

    return _cloud_api_instance

class CloudAPI:
    """云端API客户端，负责与后端服务器交互。
    - 兼容KivyMD 2.0.1 dev0
    - token格式不再校验，所有token直接信任
    - 支持X-User-ID和Bearer认证
    - 详见mobile_distribution_format.md
    """

    def __init__(self, base_url=None, timeout=DEFAULT_TIMEOUT, retry_count=DEFAULT_RETRY_COUNT, auto_switch=True):
        """初始化API客户端

        Args:
            base_url: API基础URL，为None时使用默认地址
            timeout: 请求超时时间（秒）
            retry_count: 最大重试次数
            auto_switch: 是否在公网服务器失败时自动切换到本地服务器
        """
        # 服务器配置 - 本地服务器优先
        self.server_configs = [
            {
                "name": "本地服务器",
                "url": "http://localhost:8006",  # 本地服务器使用8006端口
                "priority": 1,  # 本地服务器优先级最高
                "available": True,  # 是否可用
                "last_check": 0,  # 上次检查时间
                "failure_count": 0  # 连续失败次数
            },
            {
                "name": "本地回环地址",
                "url": "http://127.0.0.1:8006",  # 本地回环地址使用8006端口
                "priority": 2,
                "available": True,
                "last_check": 0,
                "failure_count": 0
            },
            {
                "name": "公网服务器",
                "url": "http://************",  # 公网服务器使用80端口
                "priority": 3,  # 公网服务器优先级最低
                "available": True,  # 是否可用
                "last_check": 0,  # 上次检查时间
                "failure_count": 0  # 连续失败次数
            }
        ]

        # 设置当前使用的服务器
        self.current_server_index = 0  # 默认使用第一个服务器（本地服务器）

        # 如果提供了base_url，则将其添加为最高优先级的服务器
        if base_url:
            self.server_configs.insert(0, {
                "name": "自定义服务器",
                "url": base_url,
                "priority": 0,
                "available": True,
                "last_check": 0,
                "failure_count": 0
            })

        # 默认配置
        self.base_url = self.server_configs[self.current_server_index]["url"]
        self.backup_url = self.server_configs[1]["url"] if len(self.server_configs) > 1 else None
        self.timeout = timeout
        self.auto_switch = auto_switch  # 添加自动切换标志

        # 用户认证相关属性
        self.token = None
        self.user_id = None
        self.custom_id = None  # 添加custom_id属性，用于文件上传时关联用户
        self.retry_count = retry_count

        # 记录当前使用的服务器名称
        self.current_server_name = self.server_configs[self.current_server_index]["name"]

        # 认证相关
        self.token = None
        self.refresh_token_str = None
        self.expires_at = None
        self.user_id = None
        self.custom_id = None

        # 错误信息
        self.last_error = None

        # 降级模式相关
        self.degraded_mode = False
        self.degraded_mode_until = 0
        self.server_failure_count = 0
        self.using_backup_url = False

        # 支持的文件格式
        self.supported_formats = SUPPORTED_FILE_FORMATS
        # 允许作为 health_records.record_type 的白名单（只包含后端接受的record types）
        # 这里假定常见类型，若后端有不同列表请同步更新
        self.ALLOWED_RECORD_TYPES = set([
            'basic_info', 'blood_pressure', 'blood_glucose', 'weight', 'height',
            'vaccination', 'lab_report', 'physical_exam', 'medical_record',
            'prescription', 'assessment', 'questionnaire', 'document'
        ])

        # 确保目录存在
        self.data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), DATA_DIR_NAME)
        self.queue_dir = os.path.join(self.data_dir, QUEUE_DIR_NAME)

        # 创建必要的目录
        if not os.path.exists(self.data_dir):
            try:
                os.makedirs(self.data_dir)
                logger.info(f"创建数据目录: {self.data_dir}")
            except Exception as e:
                logger.error(f"创建数据目录失败: {str(e)}")

        # 确保上传队列目录存在
        if not os.path.exists(self.queue_dir):
            try:
                os.makedirs(self.queue_dir, exist_ok=True)
                logger.info(f"创建上传队列目录: {self.queue_dir}")
            except Exception as e:
                logger.error(f"创建上传队列目录失败: {str(e)}")

        # 加载认证信息
        self.load_auth_info()

        # 记录到日志（使用debug级别减少重复日志）
        logger.debug(f"初始化API客户端，基础URL: {self.base_url}")
        logger.debug(f"文件上传超时设置: {self.timeout}秒")

        # 锁，用于保护访问令牌
        self.auth_lock = Lock()
        self._token_lock = threading.Lock()  # 添加锁来保护token访问
        
        # 初始化连接池管理器
        self.connection_pool_manager = get_connection_pool_manager()
        logger.info("已初始化HTTP连接池管理器")

    
    def _make_request(self, method, endpoint, data=None, json_data=None, headers=None, bypass_auth=False, files=None, max_retries=5, params=None, timeout=None):
        """发送HTTP请求并处理响应

        Args:
            method: HTTP方法（GET, POST等）
            endpoint: API端点路径
            data: 表单数据
            json_data: JSON数据
            headers: 请求头
            bypass_auth: 是否绕过认证
            files: 文件上传
            max_retries: 最大重试次数
            params: URL查询参数
            timeout: 超时时间（秒）

        Returns:
            dict: API响应，解析为JSON格式
        """
        # 使用辅助类处理请求
        request_helper = RequestHelper()
        response_handler = ResponseHandler()
        
        # 准备请求头
        prepared_headers = request_helper.prepare_headers(
            token=self.token if not bypass_auth else None,
            custom_id=getattr(self, 'custom_id', None) if not bypass_auth else None,
            bypass_auth=bypass_auth,
            additional_headers=headers or {}
        )
        
        # 构建URL
        url = request_helper.build_url(self.base_url, endpoint)
        
        retries = 0
        last_error = None

        while retries <= max_retries:
            try:
                logger.debug(f"发送 {method} 请求 [{url}]")

                # 准备请求参数
                request_kwargs = request_helper.prepare_request_kwargs(
                    method, url, params, data, json_data, prepared_headers, files, 
                    timeout if timeout is not None else (self.timeout if files is None else None)
                )

                # 使用连接池管理器发送HTTP请求
                http_session = self.connection_pool_manager.get_http_session()
                response = http_session.request(**request_kwargs)

                # 处理成功响应
                if response.status_code >= 200 and response.status_code < 300:
                    self.server_configs[self.current_server_index][FAILURE_COUNT_KEY] = 0
                    return response_handler.handle_success_response(response)

                # 处理服务器错误
                elif response.status_code >= 500:
                    self.server_configs[self.current_server_index][FAILURE_COUNT_KEY] += 1
                    error_msg = f"服务器返回错误状态码: {response.status_code}"
                    logger.error(error_msg)

                    if ServerManager.should_switch_server(self.server_configs, self.current_server_index):
                        logger.info(f"尝试切换服务器，当前服务器: {self.current_server_name}")
                        self._switch_server(self.current_server_index)
                        url = request_helper.build_url(self.base_url, endpoint)
                        logger.info(f"已切换到服务器: {self.current_server_name}, 新URL: {url}")
                        retries += 1
                        continue

                    if retries == max_retries:
                        return response_handler.handle_server_error(response)

                # 处理客户端错误
                else:
                    return response_handler.handle_client_error(response)

            except requests.exceptions.RequestException as e:
                error_response = response_handler.handle_request_exception(e)
                last_error = str(e)
                
                # 检查是否是连接池相关错误
                error_str = str(e).lower()
                if "too many 500 error responses" in error_str or "connection pool" in error_str:
                    logger.warning(f"检测到连接池错误: {e}，尝试重置连接池")
                    try:
                        # 重置连接池前等待一小段时间
                        time.sleep(1.0)
                        self.connection_pool_manager.reset_http_connection_pool()
                        logger.info("连接池已重置，将在下次重试时生效")
                        
                        # 如果是连接池错误，增加重试次数
                        if retries < max_retries + 3:  # 允许额外的重试次数
                            retries += 1
                            time.sleep(2)  # 等待一段时间再重试
                            continue
                    except Exception as reset_error:
                        logger.error(f"重置连接池失败: {reset_error}")

                # 记录失败并考虑切换服务器
                self.server_configs[self.current_server_index][FAILURE_COUNT_KEY] += 1

                if ServerManager.should_switch_server(self.server_configs, self.current_server_index):
                    logger.info(f"尝试切换服务器，当前服务器: {self.current_server_name}")
                    self._switch_server(self.current_server_index)
                    url = request_helper.build_url(self.base_url, endpoint)
                    logger.info(f"已切换到服务器: {self.current_server_name}, 新URL: {url}")
                    retries += 1
                    time.sleep(2)  # 等待一段时间再重试
                    continue

            retries += 1
            if retries <= max_retries:
                # 添加指数退避延迟
                delay = min(3 ** retries, 30)  # 最大延迟30秒，基础延迟3秒
                logger.info(f"请求失败，{delay}秒后进行第{retries}次重试")
                time.sleep(delay)

        return {STATUS_KEY: ERROR_STATUS, MESSAGE_KEY: f"请求失败，已重试{max_retries}次", DETAIL_KEY: str(last_error)}

    def get(self, endpoint, params=None, headers=None):
        """通用GET请求方法
        
        Args:
            endpoint: API端点路径
            params: URL查询参数
            headers: 请求头
            
        Returns:
            dict: API响应
        """
        return self._make_request(
            method="GET",
            endpoint=endpoint,
            params=params,
            headers=headers
        )
    
    def post(self, endpoint, data=None, json_data=None, headers=None, files=None):
        """通用POST请求方法
        
        Args:
            endpoint: API端点路径
            data: 表单数据
            json_data: JSON数据
            headers: 请求头
            files: 文件上传
            
        Returns:
            dict: API响应
        """
        return self._make_request(
            method="POST",
            endpoint=endpoint,
            data=data,
            json_data=json_data,
            headers=headers,
            files=files
        )
    
    def put(self, endpoint, data=None, json_data=None, headers=None):
        """通用PUT请求方法
        
        Args:
            endpoint: API端点路径
            data: 表单数据
            json_data: JSON数据
            headers: 请求头
            
        Returns:
            dict: API响应
        """
        return self._make_request(
            method="PUT",
            endpoint=endpoint,
            data=data,
            json_data=json_data,
            headers=headers
        )
    
    def delete(self, endpoint, headers=None):
        """通用DELETE请求方法
        
        Args:
            endpoint: API端点路径
            headers: 请求头
            
        Returns:
            dict: API响应
        """
        return self._make_request(
            method="DELETE",
            endpoint=endpoint,
            headers=headers
        )

    def _should_switch_server(self):
        """判断是否应该切换服务器"""
        # 如果当前服务器连续失败次数超过阈值，应该切换
        current_failures = self.server_configs[self.current_server_index]["failure_count"]
        return current_failures >= 2  # 连续失败2次以上时切换服务器

    def request_ocr(self, file_id, options=None):
        """
        请求后端对指定文件进行OCR处理。
        Args:
            file_id (str|int): 文档ID
            options (dict): OCR参数，如语言、方向检测等
        Returns:
            dict: 后端返回的OCR任务信息
        """
        endpoint = f"documents/{file_id}/ocr"
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        elif self.custom_id:
            headers['X-User-ID'] = str(self.custom_id)
        # 兼容部分后端只认X-User-ID
        if self.custom_id:
            headers['X-User-ID'] = str(self.custom_id)
        try:
            logger.info(f"请求后端OCR: file_id={file_id}, options={options}")
            resp = self._make_request(
                method="POST",
                endpoint=endpoint,
                json_data=options or {},
                headers=headers,
                max_retries=3
            )
            if resp and (resp.get('status') == 'success' or resp.get('success')):
                logger.info(f"OCR处理请求成功: {file_id}")
                return resp.get('data', resp)
            else:
                error_msg = resp.get('message', 'OCR请求失败，未知错误') if resp else 'OCR请求失败'
                logger.error(error_msg)
                self.last_error = error_msg
                return None
        except Exception as e:
            logger.error(f"请求OCR处理异常: {e}")
            self.last_error = str(e)
            return None

    def get_documents(self, page=1, page_size=20, document_type=None, custom_id=None):
        """获取文档列表

        Args:
            page (int): 页码，从1开始
            page_size (int): 每页数量
            document_type (str, optional): 文档类型过滤
            custom_id (str, optional): 用户自定义ID，用于过滤当前用户文档

        Returns:
            dict: 文档列表，包含documents和total字段
        """
        # 检查认证状态
        if not self.token:
            # 尝试加载认证信息
            self.load_auth_info()

            # 再次检查认证状态 - 修改为同时检查token和custom_id
            if not self.token and not hasattr(self, 'custom_id'):
                logger.error("未登录，无法获取文档列表")
                self.last_error = "未登录，无法获取文档列表"
                return None
            elif not self.token and hasattr(self, 'custom_id') and self.custom_id:
                logger.info(f"没有token但有custom_id: {self.custom_id}，可以使用X-User-ID认证")
                # 继续处理，将使用X-User-ID认证

        # 准备自定义头部，确保包含X-User-ID
        custom_headers = {}
        if hasattr(self, 'custom_id') and self.custom_id:
            custom_headers["X-User-ID"] = self.custom_id
            logger.info(f"添加X-User-ID头部用于获取文档列表: {self.custom_id}")

        # 准备查询参数
        params = {
            'page': page,
            'page_size': page_size
        }

        if document_type:
            params['document_type'] = document_type

        # 强制将 custom_id 作为查询参数传递，确保后端只返回当前用户文档
        if custom_id:
            params['custom_id'] = custom_id
            logger.info(f"查询参数中添加 custom_id: {custom_id}")
        elif hasattr(self, 'custom_id') and self.custom_id:
            params['custom_id'] = self.custom_id
            logger.info(f"查询参数中添加实例 custom_id: {self.custom_id}")

        # 发送获取文档列表请求
        logger.info(f"获取文档列表: 页码={page}, 每页数量={page_size}, 文档类型={document_type}")

        # 使用正确的文档API路径 /api/documents
        try:
            # 直接使用documents端点获取文档列表
            result = self._make_request(
                method="GET",
                endpoint="api/documents",  # 使用正确的文档端点
                params=params,  # 添加查询参数
                headers=custom_headers,  # 添加自定义头部
                max_retries=3  # 设置重试次数
            )
            logger.info(f"使用api/documents API路径获取文档列表")
        except Exception as e:
            logger.error(f"使用api/documents API路径获取文档列表失败: {str(e)}")
            result = None

        if result:
            # 检查不同的成功响应格式
            if result.get("status") == "success" or result.get("success"):
                # 提取数据，兼容不同的响应格式
                data = result.get("data", {})
                if not data and isinstance(result, dict):
                    # 如果没有data字段，但result本身是字典，可能整个result就是数据
                    data = result

                # 处理user-health-records端点返回的数据格式
                documents = []
                if "records" in data:
                    # user-health-records端点返回的格式
                    documents = data["records"]
                elif "documents" in data:
                    # 旧端点返回的格式
                    documents = data["documents"]
                elif "items" in data:
                    # 其他可能的格式
                    documents = data["items"]
                elif isinstance(data, list):
                    # 直接返回列表
                    documents = data
            
                # 过滤指定类型的文档
                if document_type:
                    filtered_documents = []
                    for doc in documents:
                        doc_type = doc.get("type") or doc.get("document_type")
                        if doc_type == document_type:
                            filtered_documents.append(doc)
                    documents = filtered_documents
            
                # 统一返回格式
                result_data = {
                    "documents": documents,
                    "total": len(documents),
                    "page_total": len(documents)
                }
                
                # 如果data是字典，尝试获取total和page_total字段
                if isinstance(data, dict):
                    result_data["total"] = data.get("total", len(documents))
                    result_data["page_total"] = data.get("page_total", len(documents))

                logger.info(f"成功获取文档列表，共 {len(documents)} 个文档")
                return result_data
            else:
                error_msg = result.get("message", "获取文档列表失败，未知错误")
                logger.error(f"获取文档列表失败: {error_msg}")

                self.last_error = error_msg
                return None
        else:
            error_msg = "获取文档列表失败，无法连接服务器"
            logger.error(error_msg)
            self.last_error = error_msg
            return None

    def get_mobile_assessment_templates(self, custom_id=None):
        """获取移动端评估量表模板列表

        Args:
            custom_id: 用户自定义ID

        Returns:
            dict: 评估量表模板列表
        """
        # 准备请求头
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if custom_id or self.custom_id:
            headers['X-User-ID'] = custom_id or self.custom_id
            logger.info(f"添加X-User-ID头: {custom_id or self.custom_id}")
        headers['Content-Type'] = 'application/json'

        # 准备查询参数
        params = {}
        if custom_id or self.custom_id:
            params['custom_id'] = custom_id or self.custom_id

        logger.info(f"获取移动端评估量表模板，custom_id: {custom_id or self.custom_id}")

        try:
            # 根据API文档使用mobile/templates/assessment-templates端点
            result = self._make_request(
                method="GET",
                endpoint="mobile/templates/assessment-templates",
                params=params,
                headers=headers,
                max_retries=3
            )

            # 记录原始响应用于调试
            logger.info(f"移动端评估量表模板API原始响应类型: {type(result)}")
            if isinstance(result, dict):
                logger.info(f"移动端评估量表模板API原始响应键: {list(result.keys())}")
            else:
                logger.info(f"移动端评估量表模板API原始响应: {result}")
            
            # 处理不同格式的响应
            if isinstance(result, list):
                # 服务器直接返回列表，包装成标准格式
                logger.info("检测到列表格式响应，包装为标准格式")
                return {
                    "status": "success",
                    "data": result
                }
            elif isinstance(result, dict):
                # 检查是否为成功响应
                if result.get('status') == 'success' or result.get('success'):
                    logger.info("成功获取移动端评估量表模板")
                    return result
                elif result.get('status') == 'error':
                    error_msg = result.get('message', '获取评估量表模板失败')
                    logger.error(f"获取移动端评估量表模板失败: {error_msg}")
                    self.last_error = error_msg
                    return result
                else:
                    # 没有明确状态的字典响应，假设为成功
                    logger.info(MSG_DICT_RESPONSE_SUCCESS)
                    return {
                        "status": "success",
                        "data": result
                    }
            elif isinstance(result, str):
                # 字符串响应，可能是错误信息
                logger.error(f"获取移动端评估量表返回字符串: {result}")
                self.last_error = result
                return {"status": "error", "message": result}
            else:
                # 其他类型的响应
                error_msg = f"未知响应格式: {type(result)}"
                logger.error(f"获取移动端评估量表失败: {error_msg}")
                self.last_error = error_msg
                return {"status": "error", "message": error_msg}

        except Exception as e:
            error_msg = f"获取移动端评估量表模板异常: {str(e)}"
            logger.error(error_msg)
            self.last_error = error_msg
            return {"status": "error", "message": error_msg}

    def get_mobile_assessments(self, custom_id=None, status=None):
        """获取分发给用户的评估量表列表
        
        Args:
            custom_id: 用户自定义ID
            status: 状态过滤，可选值：pending/completed
        """
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if custom_id or self.custom_id:
            headers['X-User-ID'] = custom_id or self.custom_id
        headers['Content-Type'] = 'application/json'
        
        # 构建查询参数
        params = {}
        if status:
            params['status'] = status
            
        try:
            # 使用正确的mobile API端点
            result = self._make_request(
                method="GET",
                endpoint="mobile/assessments",
                headers=headers,
                params=params,
                max_retries=3
            )
            
            # 统一返回格式处理
            if isinstance(result, dict):
                if result.get('status') == 'success' or result.get('success'):
                    # 处理数据结构转换，确保移动端兼容性
                    processed_result = self._process_assessment_data_structure(result)
                    return processed_result
                elif result.get('status') == 'error':
                    logger.error(f"获取评估列表失败: {result.get('message')}")
                    return result
                else:
                    # 没有明确状态，包装为标准格式
                    wrapped_result = {"status": "success", "data": result}
                    processed_result = self._process_assessment_data_structure(wrapped_result)
                    return processed_result
            elif isinstance(result, list):
                # 直接返回列表，包装为标准格式
                wrapped_result = {"status": "success", "data": result}
                processed_result = self._process_assessment_data_structure(wrapped_result)
                return processed_result
            else:
                error_msg = f"获取评估列表返回格式错误: {type(result)}"
                logger.error(error_msg)
                return {"status": "error", "message": error_msg}
                
        except Exception as e:
            error_msg = f"获取评估列表异常: {str(e)}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}
    
    def _process_assessment_data_structure(self, result):
        """处理评估量表数据结构，确保移动端兼容性
        
        将template.questions提取到根级别，保持template信息不变
        同时处理ID映射问题：distribution_id vs assessment_id
        去除重复问题数据，优化日志输出
        
        Args:
            result: API返回结果
            
        Returns:
            dict: 处理后的结果
        """
        try:
            if not isinstance(result, dict) or 'data' not in result:
                return result
                
            processed_data = []
            data_list = result.get('data', [])
            
            # 只在处理第一次时记录详细日志
            log_details = len(data_list) > 0
            
            for idx, item in enumerate(data_list):
                if not isinstance(item, dict):
                    processed_data.append(item)
                    continue
                    
                # 创建新的评估量表对象，保持原有数据
                processed_item = item.copy()
                
                # 处理ID映射问题：确保移动端能正确识别评估量表
                assessment_id = item.get('id')
                distribution_id = item.get('distribution_id')
                
                if distribution_id:
                    # 保持原始的assessment ID为内部使用
                    processed_item['original_assessment_id'] = assessment_id
                    # 使用distribution_id作为移动端的主要ID
                    processed_item['id'] = distribution_id
                    processed_item['assessment_id'] = assessment_id  # 保持对原始评估量表ID的引用
                    if log_details and idx == 0:  # 只为第一个记录日志
                        logger.debug(f"评估量表ID映射: distribution_id={distribution_id} -> 主ID, assessment_id={assessment_id} -> 内部ID")
                
                # 处理template信息
                template = item.get('template')
                final_id = processed_item.get('id')
                
                if isinstance(template, dict):
                    # 提取template中的questions到根级别
                    template_questions = template.get('questions', [])
                    
                    if template_questions:
                        # 高效去重算法：根据question_id去重，保留第一个出现的问题
                        seen_question_ids = set()
                        unique_questions = []
                        
                        for q in template_questions:
                            question_id = q.get('question_id')
                            if question_id and question_id not in seen_question_ids:
                                seen_question_ids.add(question_id)
                                unique_questions.append(q)
                            elif not question_id:
                                # 如果没有question_id，直接添加
                                unique_questions.append(q)
                        
                        # 按question_id排序确保一致性
                        unique_questions.sort(key=lambda x: x.get('question_id', ''))
                        processed_item['questions'] = unique_questions
                        
                        # 只在有重复时或首次处理时记录日志
                        original_count = len(template_questions)
                        final_count = len(unique_questions)
                        
                        if log_details and (original_count != final_count or idx == 0):
                            if original_count != final_count:
                                logger.info(f"✓ 评估量表{final_id}: 去重 {original_count} -> {final_count} 个问题")
                            else:
                                logger.debug(f"✓ 评估量表{final_id}: 提取了{final_count}个问题")
                                
                            # 只显示第一个问题用于验证
                            if unique_questions and idx == 0:
                                first_text = (unique_questions[0].get('question_text') or 
                                             unique_questions[0].get('text', ''))[:20] + '...'
                                logger.debug(f"[首个问题样例] {first_text}")
                    
                    # 确保有template_id字段以保持向后兼容
                    if 'template_id' not in processed_item and 'id' in template:
                        processed_item['template_id'] = template['id']
                        if log_details and idx == 0:
                            logger.debug(f"✓ 添加template_id: {template['id']}")
                
                processed_data.append(processed_item)
            
            # 返回处理后的结果
            processed_result = result.copy()
            processed_result['data'] = processed_data
            
            if processed_data:
                logger.info(f"成功处理了{len(processed_data)}个评估量表的数据结构")
            return processed_result
            
        except Exception as e:
            logger.error(f"处理评估量表数据结构失败: {str(e)}")
            # 如果处理失败，返回原始数据
            return result

    def get_mobile_assessments_new(self, custom_id=None):
        """从新API获取移动端评估量表列表

        Args:
            custom_id: 用户自定义ID

        Returns:
            dict: 评估量表列表
        """
        # 准备请求头
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if custom_id or self.custom_id:
            headers['X-User-ID'] = custom_id or self.custom_id

        try:
            # 使用新API路径
            result = self._make_request(
                method="GET",
                endpoint="mobile/assessments",
                headers=headers,
                max_retries=3
            )

            if result and (result.get('status') == 'success' or result.get('success')):
                # 处理数据结构转换，确保移动端兼容性
                processed_result = self._process_assessment_data_structure(result)
                logger.info(f"成功获取移动端评估量表列表(新API)，数量: {len(processed_result.get('data', []))}")
                return processed_result
            else:
                error_msg = result.get('message', '获取评估量表列表失败') if result else '请求失败'
                logger.error(f"获取移动端评估量表列表(新API)失败: {error_msg}")
                self.last_error = error_msg
                return {"status": "error", "message": error_msg, "data": []}

        except Exception as e:
            error_msg = f"获取移动端评估量表列表(新API)异常: {str(e)}"
            logger.error(error_msg)
            self.last_error = error_msg
            return {"status": "error", "message": error_msg, "data": []}

    def get_mobile_questionnaires(self, custom_id=None):
        """获取分发给用户的问卷列表"""
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if custom_id or self.custom_id:
            headers['X-User-ID'] = custom_id or self.custom_id
        headers['Content-Type'] = 'application/json'
        try:
            result = self._make_request(
                method="GET",
                endpoint="mobile/questionnaires",
                headers=headers,
                max_retries=3
            )
            
            # 统一返回格式处理
            if isinstance(result, dict):
                if result.get('status') == 'success' or result.get('success'):
                    return result
                elif result.get('status') == 'error':
                    logger.error(f"获取问卷列表失败: {result.get('message')}")
                    return result
                else:
                    # 没有明确状态，包装为标准格式
                    return {"status": "success", "data": result}
            elif isinstance(result, list):
                # 直接返回列表，包装为标准格式
                return {"status": "success", "data": result}
            else:
                error_msg = f"获取问卷列表返回格式错误: {type(result)}"
                logger.error(error_msg)
                return {"status": "error", "message": error_msg}
                
        except Exception as e:
            error_msg = f"获取问卷列表异常: {str(e)}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def get_mobile_questionnaires_new(self, custom_id=None):
        """从新API获取移动端问卷列表

        Args:
            custom_id: 用户自定义ID

        Returns:
            dict: 问卷列表
        """
        # 准备请求头
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if custom_id or self.custom_id:
            headers['X-User-ID'] = custom_id or self.custom_id

        try:
            # 使用新API路径
            result = self._make_request(
                method="GET",
                endpoint="mobile/questionnaires",
                headers=headers,
                max_retries=3
            )

            if result and (result.get('status') == 'success' or result.get('success')):
                logger.info(f"成功获取移动端问卷列表(新API)，数量: {len(result.get('data', []))}")
                return result
            else:
                error_msg = result.get('message', '获取问卷列表失败') if result else '请求失败'
                logger.error(f"获取移动端问卷列表(新API)失败: {error_msg}")
                self.last_error = error_msg
                return {"status": "error", "message": error_msg, "data": []}

        except Exception as e:
            error_msg = f"获取移动端问卷列表(新API)异常: {str(e)}"
            logger.error(error_msg)
            self.last_error = error_msg
            return {"status": "error", "message": error_msg, "data": []}

    def get_assessment_questions(self, assessment_id):
        """获取评估量表的问题列表
        
        Args:
            assessment_id: 评估量表ID（可能是distribution_id或者assessment_id）
            
        Returns:
            dict: 包含问题列表的字典
        """
        logger.debug(f"[获取评估量表问题] ID={assessment_id}")
        
        try:
            # 首先从基础评估量表列表中查找
            assessment = self.get_mobile_assessment(assessment_id)
            if assessment and assessment.get('status') == 'success':
                questions = assessment.get('data', {}).get('questions', [])
                logger.debug(f"[获取评估量表问题] 找到 {len(questions)} 个问题")
                return questions
            else:
                error_msg = assessment.get('message', '获取评估量表问题失败') if assessment else '请求失败'
                logger.error(f"[获取评估量表问题] 失败: {error_msg}")
                self.last_error = error_msg
                return {"status": "error", "message": error_msg, "data": []}

        except Exception as e:
            error_msg = f"获取评估量表问题异常: {str(e)}"
            logger.error(error_msg)
            self.last_error = error_msg
            return {"status": "error", "message": error_msg, "data": []}

    def register_user(self, user_data):
        """注册新用户
        
        Args:
            user_data (dict): 用户注册数据
            
        Returns:
            dict: 注册结果
        """
        try:
            # 准备请求头
            headers = {
                'Content-Type': 'application/json'
            }
            
            # 发送注册请求
            result = self._make_request(
                method="POST",
                endpoint="auth/register",
                json_data=user_data,
                headers=headers,
                bypass_auth=True,  # 注册不需要认证
                max_retries=3
            )
            
            if result and (result.get("status") == "success" or result.get("success")):
                logger.info("用户注册成功")
                return result
            else:
                error_msg = result.get("message", "注册失败，未知错误") if result else "注册请求失败"
                logger.error(f"用户注册失败: {error_msg}")
                self.last_error = error_msg
                return {"status": "error", "message": error_msg}
                
        except Exception as e:
            error_msg = f"用户注册异常: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.last_error = error_msg
            return {"status": "error", "message": error_msg}

            assessments_result = self.get_mobile_assessments()
            target_assessment = None
            available_assessments = []
            
            if assessments_result and assessments_result.get('status') == 'success':
                data_list = assessments_result.get('data', [])
                
                # 收集所有可用ID用于诊断
                for item in data_list:
                    if isinstance(item, dict):
                        assessment_info = {
                            'id': item.get('id'),
                            'distribution_id': item.get('distribution_id'), 
                            'assessment_id': item.get('assessment_id'),
                            'name': item.get('name', '未知')
                        }
                        available_assessments.append(assessment_info)
                        
                        item_id = item.get('id')
                        distribution_id = item.get('distribution_id')
                        assessment_id_field = item.get('assessment_id')
                        
                        if (item_id == assessment_id or 
                            distribution_id == assessment_id or 
                            assessment_id_field == assessment_id):
                            target_assessment = item
                            logger.debug(f"在评估量表中找到ID={assessment_id}")
                            break
                
                # 尝试获取pending评估量表
                pending_result = None
                try:
                    pending_result = self.get_mobile_assessments(status='pending')
                except Exception as e:
                    logger.warning(f"获取pending评估量表失败: {str(e)}")
                
                # 如果在基础评估量表中没有找到，再在pending评估量表中查找
                if not target_assessment and pending_result and pending_result.get('status') == 'success':
                    pending_assessments = pending_result.get('data', [])
                    
                    if pending_assessments:
                        # 对pending评估量表进行数据结构处理
                        wrapped_pending_result = {"status": "success", "data": pending_assessments}
                        processed_pending_result = self._process_assessment_data_structure(wrapped_pending_result)
                        processed_pending_assessments = processed_pending_result.get('data', [])
                        
                        # 在处理后的pending评估量表中查找
                        for item in processed_pending_assessments:
                            assessment_info = {
                                'id': item.get('id'),
                                'distribution_id': item.get('distribution_id'), 
                                'assessment_id': item.get('assessment_id'),
                                'name': item.get('name', '未知')
                            }
                            available_assessments.append(assessment_info)
                            
                            item_id = item.get('id')
                            distribution_id = item.get('distribution_id')
                            assessment_id_field = item.get('assessment_id')
                            
                            if (item_id == assessment_id or 
                                distribution_id == assessment_id or 
                                assessment_id_field == assessment_id):
                                target_assessment = item
                                logger.debug(f"在pending评估量表中找到ID={assessment_id}")
                                break
            
            # 如果找到了目标评估量表，获取问题
            if target_assessment and isinstance(target_assessment, dict):
                questions = target_assessment.get('questions', [])
                template_id = target_assessment.get('template_id')
                
                if questions:
                    logger.debug(f"✓ 找到评估量表ID={assessment_id}，问题数量: {len(questions)}")
                    return {
                        "status": "success",
                        "questions": questions,
                        "template_id": template_id,
                        "assessment_info": {
                            'id': target_assessment.get('id'),
                            'name': target_assessment.get('name'),
                            'distribution_id': target_assessment.get('distribution_id')
                        }
                    }
                
                # 如果没有直接的questions，尝试从 template 中获取
                template = target_assessment.get('template')
                if isinstance(template, dict) and 'questions' in template:
                    template_questions = template['questions']
                    if template_questions:
                        logger.debug(f"✓ 从评估量表模板中获取问题列表，共 {len(template_questions)} 个问题")
                        return {
                            "status": "success",
                            "questions": template_questions,
                            "template_id": template_id
                        }
                
                # 如果pending评估量表没有问题，尝试从同名的基础评估量表中获取问题（回退机制）
                assessment_name = target_assessment.get('name')
                if assessment_name and not questions and not template:
                    logger.debug(f"pending评估量表'{assessment_name}'没有问题，尝试从同名基础评估量表获取...")
                    
                    # 从基础评估量表列表中查找同名的量表
                    if assessments_result and assessments_result.get('status') == 'success':
                        base_data_list = assessments_result.get('data', [])
                        
                        for base_item in base_data_list:
                            if isinstance(base_item, dict):
                                base_name = base_item.get('name')
                                if base_name == assessment_name:
                                    base_questions = base_item.get('questions', [])
                                    base_template = base_item.get('template')
                                    base_template_id = base_item.get('template_id')
                                    
                                    if base_questions:
                                        logger.info(f"✓ 从同名基础评估量表获取到{len(base_questions)}个问题")
                                        return {
                                            "status": "success",
                                            "questions": base_questions,
                                            "template_id": base_template_id,
                                            "assessment_info": {
                                                'id': target_assessment.get('id'),
                                                'name': target_assessment.get('name'),
                                                'distribution_id': target_assessment.get('distribution_id')
                                            },
                                            "source": "fallback_from_base_assessment"
                                        }
                                    elif isinstance(base_template, dict) and 'questions' in base_template:
                                        base_template_questions = base_template['questions']
                                        if base_template_questions:
                                            logger.info(f"✓ 从同名基础评估量表模板获取到{len(base_template_questions)}个问题")
                                            return {
                                                "status": "success",
                                                "questions": base_template_questions,
                                                "template_id": base_template_id,
                                                "source": "fallback_from_base_template"
                                            }
                                    break
                    
                    logger.warning(f"未能从基础评估量表中找到'{assessment_name}'的问题")
            
            # 如果仍未找到，提供诊断信息
            logger.info(f"❌ 未找到评估量表ID={assessment_id}")
            logger.info(f"📋 可用的评估量表ID:")
            for info in available_assessments[:5]:  # 显示前5个
                if isinstance(info, dict):
                    logger.info(f"   - ID={info['id']}, distribution_id={info['distribution_id']}, 名称='{info['name']}'")
            
            return {
                "status": "success",
                "questions": [],
                "message": f'评估量表ID {assessment_id} 不存在',
                "available_ids": [info['id'] for info in available_assessments if isinstance(info, dict) and 'id' in info]
            }
            
        except Exception as e:
            error_msg = f"获取评估量表问题异常: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            return {
                "status": "error",
                "message": error_msg
            }

    def get_mobile_questionnaire_templates(self, custom_id=None):
        """获取移动端问卷模板列表

        Args:
            custom_id: 用户自定义ID

        Returns:
            dict: 问卷模板列表
        """
        # 准备请求头
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if custom_id or self.custom_id:
            headers['X-User-ID'] = custom_id or self.custom_id
        headers['Content-Type'] = 'application/json'

        # 准备查询参数
        params = {}
        if custom_id or self.custom_id:
            params['custom_id'] = custom_id or self.custom_id

        logger.info(f"获取移动端问卷模板，custom_id: {custom_id or self.custom_id}")

        try:
            # 根据API文档使用mobile/templates/questionnaire-templates端点
            result = self._make_request(
                method="GET",
                endpoint="mobile/templates/questionnaire-templates",
                params=params,
                headers=headers,
                max_retries=3
            )

            # 记录原始响应用于调试
            logger.info(f"移动端问卷模板API原始响应类型: {type(result)}")
            if isinstance(result, dict):
                logger.info(f"移动端问卷模板API原始响应键: {list(result.keys())}")
            else:
                logger.info(f"移动端问卷模板API原始响应: {result}")
            
            # 处理不同格式的响应
            if isinstance(result, list):
                # 服务器直接返回列表，包装成标准格式
                logger.info("检测到列表格式响应，包装为标准格式")
                result = {
                    "status": "success",
                    "data": result
                }
            elif isinstance(result, dict):
                # 检查是否为成功响应
                if result.get('status') == 'success' or result.get('success'):
                    logger.info("成功获取移动端问卷模板")
                elif result.get('status') == 'error':
                    error_msg = result.get('message', '获取问卷模板失败')
                    logger.error(f"获取移动端问卷模板失败: {error_msg}")
                    self.last_error = error_msg
                    return result
                else:
                    # 没有明确状态的字典响应，假设为成功
                    logger.info(MSG_DICT_RESPONSE_SUCCESS)
                    result = {
                        "status": "success",
                        "data": result
                    }
            elif isinstance(result, str):
                # 字符串响应，可能是错误信息
                logger.error(f"获取移动端问卷模板返回字符串: {result}")
                self.last_error = result
                return {"status": "error", "message": result}
            else:
                # 其他类型的响应
                error_msg = f"未知响应格式: {type(result)}"
                logger.error(f"获取移动端问卷模板失败: {error_msg}")
                self.last_error = error_msg
                return {"status": "error", "message": error_msg}
                
            if result and (result.get('status') == 'success' or result.get('success')):
                logger.info("成功获取移动端问卷模板")
                return result
            else:
                error_msg = result.get('message', '获取问卷模板失败') if result else '请求失败'
                logger.error(f"获取移动端问卷模板失败: {error_msg}")
                self.last_error = error_msg
                return {"status": "error", "message": error_msg}

        except Exception as e:
            error_msg = f"获取移动端问卷模板异常: {str(e)}"
            logger.error(error_msg)
            self.last_error = error_msg
            return {"status": "error", "message": error_msg}

    def get_questionnaire_detail(self, questionnaire_id, custom_id=None):
        """获取问卷详情

        Args:
            questionnaire_id: 问卷ID
            custom_id: 用户自定义ID

        Returns:
            dict: 问卷详情
        """
        # 准备请求头
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if custom_id or self.custom_id:
            headers['X-User-ID'] = custom_id or self.custom_id
            logger.info(f"添加X-User-ID头: {custom_id or self.custom_id}")
        headers['Content-Type'] = 'application/json'

        logger.info(f"获取问卷详情，ID: {questionnaire_id}")

        try:
            # 使用questionnaires/{id}端点获取问卷详情
            result = self._make_request(
                method="GET",
                endpoint=f"questionnaires/{questionnaire_id}",
                headers=headers,
                max_retries=3
            )

            # 记录原始响应用于调试
            logger.info(f"问卷详情API原始响应类型: {type(result)}")
            if isinstance(result, dict):
                logger.info(f"问卷详情API原始响应键: {list(result.keys())}")

            # 处理不同格式的响应
            if isinstance(result, dict):
                # 检查是否为成功响应
                if result.get('status') == 'success' or result.get('success'):
                    logger.info("成功获取问卷详情")
                    return result
                elif result.get('status') == 'error':
                    error_msg = result.get('message', '获取问卷详情失败')
                    logger.error(f"获取问卷详情失败: {error_msg}")
                    self.last_error = error_msg
                    return result
                else:
                    # 没有明确状态的字典响应，假设为成功
                    logger.info(MSG_DICT_RESPONSE_SUCCESS)
                    return {
                        "status": "success",
                        "data": result
                    }
            elif isinstance(result, str):
                # 字符串响应，可能是错误信息
                logger.error(f"获取问卷详情返回字符串: {result}")
                self.last_error = result
                return {"status": "error", "message": result}
            else:
                # 其他类型的响应
                error_msg = f"未知响应格式: {type(result)}"
                logger.error(f"获取问卷详情失败: {error_msg}")
                self.last_error = error_msg
                return {"status": "error", "message": error_msg}

        except Exception as e:
            error_msg = f"获取问卷详情异常: {str(e)}"
            logger.error(error_msg)
            self.last_error = error_msg
            return {"status": "error", "message": error_msg}
            
    def get_questionnaire_questions(self, questionnaire_id):
        """获取问卷的问题列表
        
        Args:
            questionnaire_id: 问卷ID
            
        Returns:
            dict: 包含问题列表的字典
        """
        # 准备请求头
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if self.custom_id:
            headers['X-User-ID'] = self.custom_id
            logger.info(f"添加X-User-ID头: {self.custom_id}")
        headers['Content-Type'] = 'application/json'
        
        logger.info(f"获取问卷问题，ID: {questionnaire_id}")
        
        try:
            # 先获取问卷详情
            result = self._make_request(
                method="GET",
                endpoint=f"questionnaires/{questionnaire_id}",
                headers=headers,
                max_retries=3
            )
            
            # 记录原始响应用于调试
            logger.info(f"问卷详情API原始响应类型: {type(result)}")
            
            if isinstance(result, dict):
                # 检查详情中是否包含问题列表
                if 'questions' in result:
                    questions = result['questions']
                    logger.info(f"从问卷详情中获取问题列表，共 {len(questions)} 个问题")
                    return {
                        "status": "success",
                        "questions": questions
                    }
                elif 'template' in result and 'questions' in result['template']:
                    questions = result['template']['questions']
                    logger.info(f"从问卷模板中获取问题列表，共 {len(questions)} 个问题")
                    return {
                        "status": "success",
                        "questions": questions
                    }
                else:
                    # 如果详情中没有问题列表，尝试专门的问题端点
                    try:
                        questions_result = self._make_request(
                            method="GET",
                            endpoint=f"questionnaires/{questionnaire_id}/questions",
                            headers=headers,
                            max_retries=3
                        )
                        
                        if isinstance(questions_result, list):
                            logger.info(f"从专门端点获取问题列表，共 {len(questions_result)} 个问题")
                            return {
                                "status": "success",
                                "questions": questions_result
                            }
                        elif isinstance(questions_result, dict) and 'questions' in questions_result:
                            logger.info(f"从专门端点获取问题列表，共 {len(questions_result['questions'])} 个问题")
                            return questions_result
                    except Exception as e:
                        logger.warning(f"从专门端点获取问题列表失败: {e}")
                    
                    # 如果没有找到问题列表，但知道问题数量，则创建默认问题
                    if 'question_count' in result and result['question_count'] > 0:
                        count = result['question_count']
                        logger.info(f"创建 {count} 个默认问题")
                        default_questions = []
                        for i in range(count):
                            default_questions.append({
                                'id': i + 1,
                                'text': f'问题 {i + 1}',
                                'type': 'radio',
                                'options': [
                                    {'value': '1', 'text': '完全不符合'},
                                    {'value': '2', 'text': '比较不符合'},
                                    {'value': '3', 'text': '不确定'},
                                    {'value': '4', 'text': '比较符合'},
                                    {'value': '5', 'text': '完全符合'}
                                ]
                            })
                        return {
                            "status": "success",
                            "questions": default_questions
                        }
                    
                    logger.warning("API返回的数据中没有找到问题列表")
                    return {
                        "status": "success",
                        "questions": []
                    }
            elif isinstance(result, list) and len(result) > 0:
                # 如果返回的是列表，可能是直接返回的问题列表
                logger.info(f"API直接返回问题列表，共 {len(result)} 个问题")
                return {
                    "status": "success",
                    "questions": result
                }
            else:
                # 其他类型的响应
                error_msg = f"未知响应格式: {type(result)}"
                logger.error(f"获取问卷问题失败: {error_msg}")
                self.last_error = error_msg
                return {"status": "error", "message": error_msg}
                
        except Exception as e:
            error_msg = f"获取问卷问题异常: {str(e)}"
            logger.error(error_msg)
            self.last_error = error_msg
            return {"status": "error", "message": error_msg}

    def submit_mobile_assessment(self, assessment_id, answers, custom_id=None, template_id=None):
        """提交移动端评估量表结果"""
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if custom_id or self.custom_id:
            headers['X-User-ID'] = custom_id or self.custom_id
        headers['Content-Type'] = 'application/json'
        submit_data = {"answers": answers}
        if template_id:
            submit_data["template_id"] = template_id
        try:
            result = self._make_request(
                method="POST",
                endpoint=f"mobile/assessments/{assessment_id}/submit",
                json_data=submit_data,
                headers=headers,
                max_retries=3
            )
            
            # 统一返回格式处理
            if isinstance(result, dict):
                if result.get('status') == 'success':
                    logger.info(f"评估提交成功: {assessment_id}")
                    return result
                elif result.get('status') == 'error':
                    logger.error(f"评估提交失败: {result.get('message')}")
                    return result
                else:
                    # 没有明确状态，假设成功
                    return {"status": "success", "data": result}
            else:
                error_msg = f"评估提交返回格式错误: {type(result)}"
                logger.error(error_msg)
                return {"status": "error", "message": error_msg}
                
        except Exception as e:
            error_msg = f"评估提交异常: {str(e)}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def submit_mobile_questionnaire(self, questionnaire_id, answers, custom_id=None, template_id=None):
        """提交移动端问卷结果"""
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if custom_id or self.custom_id:
            headers['X-User-ID'] = custom_id or self.custom_id
        headers['Content-Type'] = 'application/json'
        submit_data = {"answers": answers}
        if template_id:
            submit_data["template_id"] = template_id
        try:
            result = self._make_request(
                method="POST",
                endpoint=f"mobile/questionnaires/{questionnaire_id}/submit",
                json_data=submit_data,
                headers=headers,
                max_retries=3
            )
            
            # 统一返回格式处理
            if isinstance(result, dict):
                if result.get('status') == 'success':
                    logger.info(f"问卷提交成功: {questionnaire_id}")
                    return result
                elif result.get('status') == 'error':
                    logger.error(f"问卷提交失败: {result.get('message')}")
                    return result
                else:
                    # 没有明确状态，假设成功
                    return {"status": "success", "data": result}
            else:
                error_msg = f"问卷提交返回格式错误: {type(result)}"
                logger.error(error_msg)
                return {"status": "error", "message": error_msg}
                
        except Exception as e:
            error_msg = f"问卷提交异常: {str(e)}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def check_server_health(self):
        """检查服务器健康状态
        
        Returns:
            dict: 包含服务器状态信息的字典
        """
        try:
            # 尝试发送简单的健康检查请求
            response = self._make_request(
                "GET",
                "health",  # 假设服务器有一个health端点
                bypass_auth=True,  # 不需要认证
                max_retries=1  # 只尝试一次
            )
            
            if response and isinstance(response, dict):
                if response.get("status") == "ok":
                    return {"online": True, "status": "ok"}
                else:
                    logger.warning(f"服务器健康检查返回非正常状态: {response}")
                    return {"online": False, "status": "error", "message": "服务器状态异常"}
            else:
                logger.warning(f"服务器健康检查返回无效响应: {response}")
                return {"online": False, "status": "error", "message": "无效的健康检查响应"}
                
        except Exception as e:
            logger.error(f"服务器健康检查异常: {str(e)}")
            return {"online": False, "status": "error", "message": str(e)}
            
    def authenticate(self, username, password=None, password_hash=None, force_cloud_auth=False):
        """用户身份验证

        Args:
            username: 用户名
            password: 密码明文，与password_hash二选一
            password_hash: 密码哈希值，与password二选一
            force_cloud_auth: 是否强制使用云端认证，忽略降级模式

        Returns:
            dict: 认证结果，成功时包含认证令牌
        """
        # 导入API适配器
        from .api_adapters import adapt_login_data

        # 先检查服务器健康状态
        try:
            server_health = self.check_server_health()
            if not server_health.get('online', False) and not force_cloud_auth:
                logger.warning(f"服务器离线，跳过云端认证")
                return None
        except Exception as e:
            logger.error(f"检查服务器健康状态时出错: {e}")
            # 继续执行，尝试登录

        # 先检查是否处于降级模式
        if self.degraded_mode and not force_cloud_auth:
            logger.warning("处于降级模式，无法进行在线认证")
            return None

        # 确保用户名和密码(或哈希)至少一个有值
        if not username or (not password and not password_hash):
            logger.error("用户名和密码(或哈希)不能为空")
            return None

        try:
            # 准备认证数据
            auth_data = {
                "username": username
            }

            # 优先使用密码哈希
            if password_hash:
                auth_data["password"] = password_hash
                logger.debug(f"使用密码哈希进行认证: {username}")
            elif password:
                # 对密码进行哈希处理以与后端保持一致
                try:
                    from .password_utils import get_password_hash
                    hashed_password = get_password_hash(password)
                    auth_data["password"] = hashed_password
                    logger.debug(f"使用哈希后的密码进行认证: {username}")
                except Exception as e:
                    logger.error(f"密码哈希处理失败: {e}")
                    # 如果哈希失败，尝试使用明文密码（向后兼容）
                    auth_data["password"] = password
                    logger.warning(f"哈希失败，使用明文密码进行认证: {username}")
            else:
                logger.error("密码或密码哈希必须提供一个")
                return None

            # 发送认证请求 - 使用正确的登录端点
            response = self._make_request(
                "POST",
                "auth/login_json",  # 使用正确的移动端登录端点
                json_data=auth_data,  # 使用json_data发送JSON数据
                bypass_auth=True,  # 认证请求不需要携带认证信息
                max_retries=3  # 增加重试次数处理502错误
            )

            # 检查响应格式
            if not isinstance(response, dict):
                error_msg = "认证响应格式错误"
                logger.error(error_msg)
                return {"status": "error", "message": error_msg}
                
            # 检查响应状态
            if response.get("status") == "error":
                logger.error(f"认证失败: {response.get('message')}")
                return response
                
            # 检查是否为成功响应
            if response.get("status") != "success":
                error_msg = f"认证响应状态异常: {response.get('status')}"
                logger.error(error_msg)
                return {"status": "error", "message": error_msg}

            # 处理认证成功的情况
            access_token = response.get("access_token", "")
            token_type = response.get("token_type", "").lower()
            
            logger.debug(f"认证响应解析: access_token={access_token[:20] if access_token else 'None'}..., token_type={token_type}")

            if access_token and token_type == "bearer":
                self.token = access_token

                # 获取用户信息
                user_info = response.get("user")
                user_id = None
                custom_id = None

                if isinstance(user_info, dict):
                    user_id = user_info.get("id")

                    # 检查是否有custom_id
                    custom_id = user_info.get("custom_id")
                    if not custom_id:
                        logger.warning("后端返回的用户信息中没有custom_id，这可能导致某些功能无法正常工作")

                    logger.info(f"认证成功，获取到用户信息: {user_info.get('username')}, ID: {user_id}, custom_id: {custom_id}")

                # 保存用户ID和custom_id到实例变量
                self.user_id = user_id
                self.custom_id = custom_id
                logger.info(f"保存用户ID: {user_id} 和custom_id: {custom_id}到CloudAPI实例")

                # 保存认证信息到本地
                self.save_auth_info()  # 使用正确的save_auth_info方法

                # 返回认证结果
                return {
                    "status": "success",
                    "token": access_token,
                    "user_id": user_id,
                    "custom_id": custom_id,  # 添加custom_id
                    "user_info": user_info
                }
            else:
                logger.error(f"认证成功但未获取到有效令牌: {response}")
                return {
                    "status": "error",
                    "message": "未获取到有效令牌",
                    "data": response
                }

        except Exception as e:
            logger.exception(f"认证过程出错: {str(e)}")
            return {
                "status": "error",
                "message": f"认证失败: {str(e)}"
            }

    def refresh_token(self):
        """刷新认证token"""
        if not self.token:
            logger.error("没有token可刷新")
            self.last_error = "没有token可刷新"
            return False

        # 根据API文档，刷新token应使用JSON格式
        # 发送刷新请求
        result = self._make_request(
            method="POST",
            endpoint="auth/refresh",
            json_data={"refresh_token": self.refresh_token_str} if self.refresh_token_str else {},
            headers={"Authorization": f"Bearer {self.token}"},
            bypass_auth=True
        )

        if result and (result.get("status") == "success" or result.get("success")):
            # 更新token信息
            resp_data = result.get("data", {})
            self.token = resp_data.get("token", self.token)
            self.refresh_token_str = resp_data.get("refresh_token", self.refresh_token_str)
            self.expires_at = resp_data.get("expires_at", self.expires_at)

            # 保存更新后的认证信息
            self.save_auth_info()

            return True

        return False

    def logout(self):
        """用户退出登录"""
        if not self.token:
            return True

        # 发送登出请求
        self._make_request(
            method="POST",
            endpoint="auth/logout",
            headers={"Authorization": f"Bearer {self.token}"}
        )

        # 无论服务器返回什么，都清除本地token
        self.token = None
        self.refresh_token_str = None
        self.expires_at = None
        self.user_id = None
        self.custom_id = None

        # 清除保存的认证信息
        try:
            auth_file = os.path.join(self.data_dir, 'cloud_auth.json')
            if os.path.exists(auth_file):
                os.remove(auth_file)
                logger.info("已删除认证信息文件")
        except Exception as e:
            logger.error(f"删除认证信息文件失败: {str(e)}")

        return True

    def save_auth_info(self):
        """保存认证信息到文件"""
        try:
            # 确保数据目录存在
            if not os.path.exists(self.data_dir):
                os.makedirs(self.data_dir, exist_ok=True)

            # 使用统一的文件名 cloud_auth.json
            auth_file = os.path.join(self.data_dir, 'cloud_auth.json')

            # 准备认证信息
            auth_info = {
                "token": self.token,
                "refresh_token": self.refresh_token_str,
                "user_id": self.user_id,
                "custom_id": self.custom_id,  # 保存custom_id字段
                "expires_at": self.expires_at
            }
            
            # 尝试获取用户名，用于检测访客登录
            try:
                from .user_manager import get_user_manager
                user_manager = get_user_manager()
                current_user = user_manager.get_current_user()
                if current_user:
                    auth_info["username"] = getattr(current_user, 'username', '')
            except (ImportError, ModuleNotFoundError) as e:
                logger.warning(f"无法导入用户管理器: {e}")
            except Exception as e:
                logger.error(f"获取用户名时出错: {str(e)}")

            # 保存认证信息
            with open(auth_file, 'w', encoding='utf-8') as f:
                json.dump(auth_info, f)

            logger.info(f"认证信息已保存，用户ID: {self.user_id}, custom_id: {self.custom_id}")
        except Exception as e:
            logger.error(f"保存认证信息失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    def load_auth_info(self):
        """从文件加载认证信息"""
        try:
            # 使用统一的文件名 cloud_auth.json
            auth_file = os.path.join(self.data_dir, 'cloud_auth.json')
            if os.path.exists(auth_file):
                with open(auth_file, 'r', encoding='utf-8') as f:
                    auth_info = json.load(f)

                # 检查是否是访客登录，如果是则不加载认证信息
                username = auth_info.get("username", "")
                if username and (username.lower() == "guest" or username.lower() == "访客"):
                    logger.info("检测到访客登录信息，不加载认证信息")
                    # 清除认证信息
                    self.token = None
                    self.refresh_token_str = None
                    self.user_id = None
                    self.custom_id = None
                    self.expires_at = None
                    return

                # 正常加载认证信息
                self.token = auth_info.get("token")
                self.refresh_token_str = auth_info.get("refresh_token")
                self.user_id = auth_info.get("user_id")
                self.custom_id = auth_info.get("custom_id")
                self.expires_at = auth_info.get("expires_at")
                
                logger.info(f"成功加载认证信息，用户ID: {self.user_id}, custom_id: {self.custom_id}")
                
                # 如果有token，验证其有效性
                if self.token:
                    logger.info(f"加载了token: {self.token[:20]}...")
                else:
                    logger.info("未找到token，但有custom_id可用于X-User-ID认证")
            else:
                logger.info("认证信息文件不存在，未加载认证信息")
        except Exception as e:
            logger.error(f"加载认证信息失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    def _check_degraded_mode(self):
        """检查是否仍在降级模式"""
        if not self.degraded_mode:
            return False

        now = time.time()
        if now > self.degraded_mode_until:
            # 降级模式已过期，尝试恢复正常模式
            self.degraded_mode = False
            logger.info("降级模式已过期，恢复正常模式")
            return False

        return True

    def _switch_server(self, failed_server_index=None):
        """切换到下一个可用的服务器

        Args:
            failed_server_index: 失败的服务器索引，如果提供，将标记该服务器为不可用

        Returns:
            bool: 是否成功切换到新服务器
        """
        # 如果指定了失败的服务器，标记它
        if failed_server_index is not None:
            self.server_configs[failed_server_index]["failure_count"] += 1
            self.server_configs[failed_server_index]["last_check"] = time.time()

            # 如果连续失败次数超过阈值，标记为不可用
            if self.server_configs[failed_server_index]["failure_count"] >= 3:
                self.server_configs[failed_server_index]["available"] = False
                logger.warning(f"服务器 {self.server_configs[failed_server_index]['name']} 连续失败3次，标记为不可用")

        # 按优先级排序可用的服务器
        available_servers = [
            (i, server) for i, server in enumerate(self.server_configs)
            if server["available"] and i != self.current_server_index
        ]
        available_servers.sort(key=lambda x: x[1]["priority"])

        # 如果没有可用的服务器，尝试重置服务器状态
        if not available_servers:
            logger.warning("没有可用的服务器，尝试重置服务器状态")
            # 重置所有服务器状态
            for server in self.server_configs:
                server["failure_count"] = 0
                server["available"] = True
                server["last_check"] = 0
            
            # 重置到第一个服务器（本地服务器）
            self.current_server_index = 0
            self.base_url = self.server_configs[0]["url"]
            self.current_server_name = self.server_configs[0]["name"]
            
            # 更新备用URL
            if len(self.server_configs) > 1:
                self.backup_url = self.server_configs[1]["url"]
            else:
                self.backup_url = None
                
            logger.info(f"服务器状态已重置，切换到: {self.current_server_name} ({self.base_url})")
            return True

        # 切换到优先级最高的可用服务器
        new_server_index, new_server = available_servers[0]
        self.current_server_index = new_server_index
        self.base_url = new_server["url"]
        self.current_server_name = new_server["name"]

        # 更新备用URL
        backup_servers = [
            server for i, server in enumerate(self.server_configs)
            if server["available"] and i != self.current_server_index
        ]
        if backup_servers:
            backup_servers.sort(key=lambda x: x["priority"])
            self.backup_url = backup_servers[0]["url"]
        else:
            self.backup_url = None

        logger.info(f"切换到服务器: {self.current_server_name} ({self.base_url})")
        return True

    def reset_server_status(self):
        """重置所有服务器状态，重新从本地服务器开始尝试连接
        
        Returns:
            bool: 是否成功重置
        """
        try:
            # 重置所有服务器的失败计数和可用状态
            for server in self.server_configs:
                server["failure_count"] = 0
                server["available"] = True
                server["last_check"] = 0
            
            # 重置到第一个服务器（本地服务器）
            self.current_server_index = 0
            self.base_url = self.server_configs[0]["url"]
            self.current_server_name = self.server_configs[0]["name"]
            
            # 更新备用URL
            if len(self.server_configs) > 1:
                self.backup_url = self.server_configs[1]["url"]
            else:
                self.backup_url = None
            
            logger.info(f"已重置服务器状态，当前使用: {self.current_server_name} ({self.base_url})")
            return True
            
        except Exception as e:
            logger.error(f"重置服务器状态时出错: {str(e)}")
            return False

    def is_in_local_mode(self):
        """检查是否处于本地模式（降级模式）

        Returns:
            bool: 是否处于本地模式
        """
        return self._check_degraded_mode()

    def is_authenticated(self):
        """检查是否已认证，包括token认证和custom_id认证

        Returns:
            bool: 是否已认证
        """
        # 如果有token，则认为已认证
        if self.token is not None:
            return True

        # 如果没有token但有custom_id，也认为已认证（可以使用X-User-ID认证）
        if hasattr(self, 'custom_id') and self.custom_id:
            logger.info(f"没有token但有custom_id: {self.custom_id}，可以使用X-User-ID认证")
            return True

        # 尝试从用户管理器获取custom_id
        try:
            from .user_manager import get_user_manager
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()
            if current_user and hasattr(current_user, 'custom_id') and current_user.custom_id:
                # 更新实例的custom_id
                self.custom_id = current_user.custom_id
                logger.info(f"从用户管理器获取到custom_id: {self.custom_id}，可以使用X-User-ID认证")
                # 保存认证信息
                self.save_auth_info()
                return True
        except Exception as e:
            logger.error(f"尝试从用户管理器获取custom_id时出错: {str(e)}")

        return False

    def get_upload_queue_size(self):
        """获取上传队列大小

        Returns:
            int: 上传队列中的文件数量
        """
        try:
            if not os.path.exists(self.queue_dir):
                return 0

            # 获取队列中的文件数量
            files = [f for f in os.listdir(self.queue_dir) if os.path.isfile(os.path.join(self.queue_dir, f))]
            return len(files)
        except Exception as e:
            logger.error(f"获取上传队列大小时出错: {str(e)}")
            return 0

    def add_to_upload_queue(self, file_path, metadata=None):
        """将文件添加到上传队列，以便稍后上传

        Args:
            file_path (str): 文件路径
            metadata (dict, optional): 文件元数据

        Returns:
            bool: 是否成功添加到队列
        """
        try:
            # 确保队列目录存在
            if not os.path.exists(self.queue_dir):
                os.makedirs(self.queue_dir, exist_ok=True)
                logger.info(f"创建上传队列目录: {self.queue_dir}")

            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"文件不存在，无法添加到上传队列: {file_path}")
                return False

            # 生成队列文件名
            import uuid
            import time
            queue_id = str(uuid.uuid4())
            timestamp = int(time.time())
            file_name = os.path.basename(file_path)
            queue_file_name = f"{timestamp}_{queue_id}_{file_name}.json"
            queue_file_path = os.path.join(self.queue_dir, queue_file_name)

            # 准备队列项数据
            meta = metadata or {}

            # 确保元数据中包含用户ID
            if 'user_id' not in meta or not meta['user_id']:
                # 优先使用custom_id
                if hasattr(self, 'custom_id') and self.custom_id:
                    meta['user_id'] = self.custom_id
                    logger.info(f"在上传队列元数据中添加custom_id: {self.custom_id}")
                # 如果没有custom_id，尝试从用户管理器获取
                else:
                    try:
                        from .user_manager import get_user_manager
                        user_manager = get_user_manager()
                        custom_id = user_manager.get_current_user_custom_id()
                        if custom_id:
                            meta['user_id'] = custom_id
                            logger.info(f"在上传队列元数据中添加从用户管理器获取的custom_id: {custom_id}")
                    except (ImportError, ModuleNotFoundError) as e:
                        logger.warning(f"无法导入用户管理器: {e}")
                    except Exception as e:
                        logger.error(f"尝试从用户管理器获取custom_id时出错: {str(e)}")

            queue_item = {
                "file_path": file_path,
                "metadata": meta,
                "created_at": timestamp,
                "status": "pending"
            }

            # 保存队列项
            with open(queue_file_path, 'w', encoding='utf-8') as f:
                json.dump(queue_item, f, ensure_ascii=False)

            logger.info(f"文件已添加到上传队列: {file_path}")
            return True

        except Exception as e:
            logger.error(f"添加文件到上传队列时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def process_upload_queue(self, max_items=5):
        """处理上传队列中的文件
        
        Args:
            max_items: 最大处理项数
            
        Returns:
            tuple: (成功数量, 失败数量)
        """
        logger.info("处理上传队列")
        
        # 检查队列目录是否存在
        if not os.path.exists(self.queue_dir):
            try:
                os.makedirs(self.queue_dir, exist_ok=True)
                logger.info(f"创建上传队列目录: {self.queue_dir}")
            except Exception as e:
                logger.error(f"创建上传队列目录失败: {str(e)}")
                return 0, 0
                
        # 检查认证状态
        if not self.is_authenticated():
            logger.info("用户未登录，跳过上传队列处理")
            return 0, 0
            
        # 获取队列文件列表
        queue_files = []
        try:
            for file_name in os.listdir(self.queue_dir):
                if file_name.endswith('.json'):
                    queue_files.append(file_name)
        except Exception as e:
            logger.error(f"读取上传队列目录失败: {str(e)}")
            return 0, 0
            
        # 如果队列为空，直接返回
        if not queue_files:
            logger.info("上传队列为空，无需处理")
            return 0, 0
            
        logger.info(f"发现 {len(queue_files)} 个待上传文件")
        
        # 限制处理数量
        queue_files = queue_files[:max_items]
        
        # 处理队列
        success_count = 0
        fail_count = 0
        
        try:
            for file_name in queue_files:
                try:
                    # 读取队列项
                    queue_file_path = os.path.join(self.queue_dir, file_name)
                    with open(queue_file_path, 'r', encoding='utf-8') as f:
                        queue_item = json.load(f)
                    
                    # 检查是否设置了next_retry时间，如果设置了且未到时间，则跳过此次处理
                    if 'next_retry' in queue_item and queue_item['next_retry'] > int(time.time()):
                        # 计算剩余等待时间
                        wait_minutes = (queue_item['next_retry'] - int(time.time())) / 60
                        logger.info(f"队列项 {file_name} 的重试时间未到，还需等待 {wait_minutes:.1f} 分钟，跳过此次处理")
                        continue
                        
                    # 获取文件路径和元数据
                    file_path = queue_item.get("file_path")
                    metadata = queue_item.get("metadata", {})
                    
                    # 检查文件路径是否有效
                    if not file_path:
                        logger.error(f"队列项缺少文件路径: {queue_file_path}")
                        try:
                            os.remove(queue_file_path)
                            logger.info(f"已删除缺少文件路径的队列项: {queue_file_path}")
                        except Exception as del_err:
                            logger.warning(f"无法删除队列文件: {queue_file_path}, 错误: {str(del_err)}")
                        fail_count += 1
                        continue
                    
                    # 检查文件是否存在
                    if not os.path.exists(file_path):
                        logger.error(f"队列中的文件不存在: {file_path}")
                        try:
                            os.remove(queue_file_path)  # 删除无效的队列项
                            logger.info(f"已删除指向不存在文件的队列项: {queue_file_path}")
                        except Exception as del_err:
                            logger.warning(f"无法删除队列文件: {queue_file_path}, 错误: {str(del_err)}")
                        fail_count += 1
                        continue
                    
                    # 检查文件是否可读
                    try:
                        with open(file_path, 'rb') as f:
                            # 只读取一小部分确认文件可访问
                            f.read(1024)
                    except Exception as e:
                        logger.error(f"队列中的文件无法读取: {file_path}, 错误: {str(e)}")
                        try:
                            os.remove(queue_file_path)  # 删除无效的队列项
                            logger.info(f"已删除指向无法读取文件的队列项: {queue_file_path}")
                        except Exception as del_err:
                            logger.warning(f"无法删除队列文件: {queue_file_path}, 错误: {str(del_err)}")
                        fail_count += 1
                        continue
                    
                    # 上传文件
                    logger.info(f"从队列中上传文件: {file_path}")
                    result = self.upload_file(file_path, metadata)
                    
                    if result:
                        logger.info(f"队列文件上传成功: {file_path}")
                        try:
                            os.remove(queue_file_path)  # 删除队列项
                            logger.info(f"已删除成功处理的队列项: {queue_file_path}")
                        except Exception as del_err:
                            logger.warning(f"无法删除队列文件: {queue_file_path}, 错误: {str(del_err)}")
                        success_count += 1
                    else:
                        logger.error(f"队列文件上传失败: {file_path}, 错误: {self.last_error if hasattr(self, 'last_error') else '未知错误'}")
                        # 检查是否需要重试
                        retry_count = queue_item.get("retry_count", 0) + 1
                        if retry_count <= 3:  # 最多重试3次
                            queue_item["retry_count"] = retry_count
                            # 使用指数退避策略
                            next_retry = int(time.time()) + (2 ** retry_count) * 60  # 2^重试次数 分钟后重试
                            queue_item["next_retry"] = next_retry
                            try:
                                with open(queue_file_path, 'w', encoding='utf-8') as f:
                                    json.dump(queue_item, f, ensure_ascii=False)
                                logger.info(f"队列项已更新重试信息，将在 {(next_retry - int(time.time())) / 60:.1f} 分钟后重试: {file_path}")
                            except Exception as e:
                                logger.error(f"更新队列项重试信息失败: {str(e)}")
                                fail_count += 1
                        else:
                            logger.warning(f"队列文件 {file_path} 已达到最大重试次数，放弃处理")
                            try:
                                os.remove(queue_file_path)  # 删除失败的队列项
                                logger.info(f"已删除达到最大重试次数的队列项: {queue_file_path}")
                            except Exception as del_err:
                                logger.warning(f"无法删除队列文件: {queue_file_path}, 错误: {str(del_err)}")
                            fail_count += 1
                except Exception as e:
                    logger.error(f"处理队列文件 {file_name} 失败: {str(e)}")
                    import traceback
                    logger.error(traceback.format_exc())
                    fail_count += 1
        except Exception as e:
            logger.error(f"处理上传队列时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
        logger.info(f"上传队列处理完成: {success_count} 个成功, {fail_count} 个失败")
        return success_count, fail_count

    def get_auth_token(self):
        """获取当前认证令牌
        
        Returns:
            str: 认证令牌，如果未认证则返回None
        """
        # 使用线程锁保护token访问
        with self._token_lock:
            # 检查token是否存在
            if self.token:
                return self.token
            else:
                logger.warning("尝试获取认证令牌，但未找到有效令牌")
                return None

    def _safe_record_type(self, incoming_type: str) -> str | None:
        """
        将外部传入的 document_type/file_type 映射为后端允许的 record_type。
        如果无法映射则返回 None（表示不在params中传递record_type）。
        这里只做保守映射；如需扩展，请在客户端与后端达成一致。
        """
        if not incoming_type:
            return None

        t = str(incoming_type).strip().lower()

        # 直接允许的类型
        if t in self.ALLOWED_RECORD_TYPES:
            return t

        # 常见 document_type 到 record_type 的映射（保守处理）
        mapping = {
            'lab_report': 'lab_report',
            'physical_exam': 'physical_exam',
            'medical_record': 'medical_record',
            'prescription': 'prescription',
            'document': 'document',
            'assessment': 'assessment',
            'questionnaire': 'questionnaire'
        }

        if t in mapping and mapping[t] in self.ALLOWED_RECORD_TYPES:
            return mapping[t]

        # 不安全或未知类型，不传递到后端作为record_type
        logger.debug(f"_safe_record_type: 拒绝不在白名单内的类型: {incoming_type}")
        return None

    def upload_file(self, file_path, metadata=None, document_type=None, description=None):
        """上传文件到后端服务器，兼容KivyMD 2.0.1dev0，支持custom_id、document_type等元数据"""
        if not self.is_authenticated():
            logger.error("未认证，无法上传文件")
            self.last_error = "未认证，无法上传文件"
            return None
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            self.last_error = f"文件不存在: {file_path}"
            return None
        try:
            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_content = f.read()
            file_name = os.path.basename(file_path)
            logger.info(f"准备上传文件: {file_name}, 大小: {len(file_content)} 字节")
            
            # 确定文件MIME类型
            import mimetypes
            mime_type = mimetypes.guess_type(file_path)[0]
            if not mime_type:
                # 根据扩展名推断MIME类型
                ext = os.path.splitext(file_path)[1].lower()
                mime_map = {
                    '.jpg': 'image/jpeg',
                    '.jpeg': 'image/jpeg',
                    '.png': 'image/png',
                    '.gif': 'image/gif',
                    '.pdf': 'application/pdf',
                    '.doc': 'application/msword',
                    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    '.xls': 'application/vnd.ms-excel',
                    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    '.txt': 'text/plain'
                }
                mime_type = mime_map.get(ext, 'application/octet-stream')
            
            logger.info(f"文件MIME类型: {mime_type}")
            
            # 组装元数据并做保守过滤：不要把后端不接受的字段传上去
            meta = metadata.copy() if metadata else {}
            if document_type:
                meta['document_type'] = document_type
            if description:
                meta['description'] = description
            # 把可能的 patient_id 映射到 custom_id，防止直接传 patient_id
            if 'patient_id' in meta:
                # 如果已有 custom_id，则保留 custom_id；否则把 patient_id 作为 custom_id
                if 'custom_id' not in meta:
                    meta['custom_id'] = meta.get('patient_id')
                # 移除 patient_id，后端不接受移动端发送的 patient_id 字段
                meta.pop('patient_id', None)
            # 移除客户端专有或后端可能拒绝的字段
            for banned in ['is_current', 'record_type', 'patient_id']:
                if banned in meta:
                    meta.pop(banned, None)
            # 添加默认 custom_id（若仍然缺失）
            if 'custom_id' not in meta and self.custom_id:
                meta['custom_id'] = self.custom_id
            
            # 适配表单数据
            form_data = {}
            for k, v in meta.items():
                if v is not None:
                    form_data[k] = str(v)
            
            # 准备文件数据
            files = {'file': (file_name, file_content, mime_type)}
            
            # 添加认证头
            headers = {}
            if self.token:
                headers['Authorization'] = f"Bearer {self.token}"
            if self.custom_id:
                headers['X-User-ID'] = self.custom_id
            
            logger.info(f"发送文件上传请求: {file_name}, 元数据: {form_data}")
            
            # 发送POST请求 - 使用移动端专用上传端点
            result = self._make_request(
                method="POST",
                endpoint="mobile/upload",
                data=form_data,
                files=files,
                headers=headers
            )
            
            # 处理响应
            if result and (result.get("status") == "success" or result.get("success")):
                logger.info(f"文件上传成功: {file_name}")
                return result
            else:
                error_msg = result.get("message", "上传失败，未知错误") if result else "上传请求失败"
                error_detail = result.get("detail", "") if result else ""
                if error_detail:
                    logger.error(f"文件上传失败详情: {error_detail}")
                    error_msg = f"{error_msg}: {error_detail}" if error_msg else f"错误: {error_detail}"
                
                logger.error(f"文件上传失败: {error_msg}")
                self.last_error = error_msg
                return {"success": False, "message": error_msg}
        except Exception as e:
            error_msg = f"上传文件时出错: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.last_error = error_msg
            return None

    def download_document(self, document_id, save_path):
        """下载文档到指定路径
        
        Args:
            document_id: 文档ID
            save_path: 保存路径
            
        Returns:
            bool: 下载是否成功
        """
        if not self.is_authenticated():
            logger.error("未认证，无法下载文档")
            self.last_error = "未认证，无法下载文档"
            return False
            
        try:
            logger.info(f"开始下载文档: {document_id} 到 {save_path}")
            
            # 准备请求头
            headers = {}
            if self.token:
                headers['Authorization'] = f"Bearer {self.token}"
            if self.custom_id:
                headers['X-User-ID'] = self.custom_id
                
            # 发送下载请求，使用正确的API端点
            url = f"{self.base_url}/api/documents/{document_id}/download"
            
            logger.info(f"文档下载URL: {url}")
            logger.info(f"请求头: {headers}")
            
            response = requests.get(
                url,
                headers=headers,
                timeout=self.timeout,
                stream=True  # 流式下载，适合大文件
            )
            
            logger.info(f"下载响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 确保保存目录存在
                save_dir = os.path.dirname(save_path)
                if save_dir and not os.path.exists(save_dir):
                    os.makedirs(save_dir, exist_ok=True)
                    
                # 写入文件
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            
                logger.info(f"文档下载成功: {save_path}")
                return True
            else:
                error_msg = f"下载文档失败，HTTP状态码: {response.status_code}"
                try:
                    error_data = response.json()
                    if 'message' in error_data:
                        error_msg += f", 错误信息: {error_data['message']}"
                        logger.error(f"服务器返回错误信息: {error_data['message']}")
                except:
                    logger.error(f"响应内容: {response.text}")
                    
                logger.error(error_msg)
                self.last_error = error_msg
                return False
                
        except requests.exceptions.RequestException as e:
            error_msg = f"下载文档时网络错误: {str(e)}"
            logger.error(error_msg)
            self.last_error = error_msg
            return False
        except Exception as e:
            error_msg = f"下载文档时出错: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.last_error = error_msg
            return False

    def delete_document(self, document_id, document_type=None, custom_id=None):
        """删除文档
        
        Args:
            document_id: 文档ID
            document_type: 文档类型（可选）
            custom_id: 用户custom_id（可选）
            
        Returns:
            dict: 删除结果
        """
        if not self.is_authenticated():
            logger.error("未认证，无法删除文档")
            self.last_error = "未认证，无法删除文档"
            return {"status": "error", "message": "未认证，无法删除文档"}
            
        try:
            logger.info(f"开始删除文档: {document_id}")
            
            # 准备请求头
            headers = {}
            if self.token:
                headers['Authorization'] = f"Bearer {self.token}"
            if custom_id or self.custom_id:
                headers['X-User-ID'] = custom_id or self.custom_id
                
            # 发送删除请求
            endpoint = f"documents/{document_id}"
            result = self._make_request(
                method="DELETE",
                endpoint=endpoint,
                headers=headers
            )
            
            # 处理删除响应
            if result:
                # 检查HTTP状态码（204表示删除成功）
                if result.get("status_code") == 204:
                    logger.info(f"文档删除成功 (HTTP 204): {document_id}")
                    return {"status": "success", "message": "文档删除成功"}
                # 检查响应状态
                elif result.get("status") == "success":
                    logger.info(f"文档删除成功: {document_id}")
                    return {"status": "success", "message": "文档删除成功"}
                elif result.get("status") == "error":
                    error_msg = result.get("message", "删除失败")
                    logger.error(f"文档删除失败: {error_msg}")
                    self.last_error = error_msg
                    return {"status": "error", "message": error_msg}
                else:
                    # 对于没有明确状态的响应，检查是否有错误信息
                    if "error" in str(result).lower() or "fail" in str(result).lower():
                        error_msg = str(result)
                        logger.error(f"文档删除可能失败: {error_msg}")
                        self.last_error = error_msg
                        return {"status": "error", "message": error_msg}
                    else:
                        # 假设删除成功
                        logger.info(f"文档删除可能成功 (无明确状态): {document_id}")
                        return {"status": "success", "message": "文档删除成功"}
            else:
                error_msg = "删除请求失败，服务器无响应"
                logger.error(error_msg)
                self.last_error = error_msg
                return {"status": "error", "message": error_msg}
        except Exception as e:
            error_msg = f"删除文档时出错: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.last_error = error_msg
            return {"status": "error", "message": error_msg}

    def create_health_diary(self, diary_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """创建健康日记到云端
        
        Args:
            diary_data (dict): 健康日记数据
            {
                'custom_id': '用户ID',
                'diary_type': '日记类型',
                'title': '标题',
                'content': '内容',
                'mood_level': 情绪等级,
                'energy_level': 能量等级,
                'pain_level': 疼痛等级,
                'diary_date': '日记日期',
                'notes': '备注'
            }

        Returns:
            dict: 创建结果，包含success状态和数据
        """
        # 检查认证状态
        if not self.is_authenticated():
            logger.error("未认证，无法创建健康日记")
            self.last_error = "未认证，无法创建健康日记"
            return {"success": False, "message": "未认证，无法创建健康日记"}
            
        try:
            # 准备请求头
            headers = {}
            if self.token:
                headers['Authorization'] = f"Bearer {self.token}"
            if self.custom_id:
                headers['X-User-ID'] = self.custom_id
                logger.info(f"添加X-User-ID头: {self.custom_id}")
            headers['Content-Type'] = 'application/json'
            
            logger.info(f"创建健康日记到云端: custom_id={diary_data.get('custom_id')}")
            
            # 调用API创建健康日记
            result = self._make_request(
                method="POST",
                endpoint="health-diaries/",
                json_data=diary_data,
                headers=headers,
                max_retries=3
            )
            
            if result:
                logger.info("健康日记创建成功")
                # 处理后端返回的数据格式
                if isinstance(result, dict) and "data" in result:
                    data = result["data"]
                    return {
                        "success": True,
                        "data": data
                    }
                elif isinstance(result, dict) and result.get("success"):
                    return result
                else:
                    logger.warning(f"未知的响应格式: {type(result)}, 内容: {result}")
                    return {
                        "success": True,
                        "data": result
                    }
            else:
                logger.error("健康日记创建失败: 空响应")
                return {
                    "success": False,
                    "message": "健康日记创建失败: 空响应"
                }
        except Exception as e:
            error_msg = f"健康日记创建异常: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.last_error = error_msg
            return {
                "success": False,
                "message": error_msg
            }

        self.last_error = error_msg
        return {"status": "error", "message": error_msg}
                
    def get_health_diary(self, diary_id, custom_id=None):
        """获取健康日记详情
        
        Args:
            diary_id: 日记ID
            custom_id: 用户自定义ID
            
        Returns:
            dict: 健康日记详情
        """
        # 确保有custom_id
        user_id = custom_id or self.custom_id
        if not user_id:
            logger.error("获取健康日记详情失败: 缺少用户ID")
            return None
            
        # 准备请求头
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if user_id:
            headers['X-User-ID'] = user_id
            logger.info(f"添加X-User-ID头: {user_id}")
        headers['Content-Type'] = 'application/json'
        
        logger.info(f"获取健康日记详情: diary_id={diary_id}, custom_id={user_id}")
        
        try:
            # 调用API获取健康日记详情
            result = self._make_request(
                method="GET",
                endpoint=f"health-diaries/{diary_id}",
                headers=headers,
                max_retries=3
            )
            
            if result:
                logger.info("健康日记详情获取成功")
                # 处理后端返回的数据格式
                if isinstance(result, dict) and "data" in result:
                    data = result["data"]
                    return {
                        "status": "success",
                        "data": data
                    }
                elif isinstance(result, dict) and result.get("status") == "success":
                    return result
                else:
                    logger.warning(f"未知的响应格式: {type(result)}, 内容: {result}")
                    return {
                        "status": "success",
                        "data": result
                    }
            else:
                logger.error("健康日记详情获取失败: 空响应")
                return {
                    "status": "error",
                    "message": "健康日记详情获取失败: 空响应"
                }
        except Exception as e:
            error_msg = f"健康日记详情获取异常: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.last_error = error_msg
            return {
                "status": "error",
                "message": error_msg
            }

    def get_health_diaries(self, custom_id=None, diary_type=None, start_date=None, end_date=None, page=1, limit=100):
        """获取健康日记列表
        
        Args:
            custom_id: 用户自定义ID
            diary_type: 日记类型过滤
            start_date: 开始日期
            end_date: 结束日期
            page: 页码
            limit: 每页数量
            
        Returns:
            dict: 健康日记列表
        """
        # 确保有custom_id
        user_id = custom_id or self.custom_id
        if not user_id:
            logger.error("获取健康日记列表失败: 缺少用户ID")
            return {
                "success": False,
                "message": "获取健康日记列表失败: 缺少用户ID"
            }
        
        # 准备请求头
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if user_id:
            headers['X-User-ID'] = user_id
            logger.info(f"添加X-User-ID头: {user_id}")
        headers['Content-Type'] = 'application/json'
        
        # 准备查询参数
        params = {
            'skip': (page - 1) * limit,
            'limit': limit
        }
        
        if diary_type:
            params['diary_type'] = diary_type
        if start_date:
            params['start_date'] = start_date
        if end_date:
            params['end_date'] = end_date
            
        logger.info(f"获取健康日记列表: custom_id={user_id}")
        
        try:
            # 调用API获取健康日记列表
            result = self._make_request(
                method="GET",
                endpoint=f"health-diaries/user/{user_id}",
                params=params,
                headers=headers,
                max_retries=3
            )
            
            if result:
                logger.info("健康日记列表获取成功")
                # 处理后端返回的数据格式
                if isinstance(result, dict) and "data" in result:
                    data = result["data"]
                    total = result.get("total", len(data))
                    logger.info(f"成功获取健康日记列表，共 {total} 条")
                    
                    return {
                        "status": "success",
                        "data": data,
                        "total": total
                    }
                elif isinstance(result, dict) and result.get("status") == "success":
                    return result
                else:
                    logger.warning(f"未知的响应格式: {type(result)}, 内容: {result}")
                    return {
                        "status": "success",
                        "data": result
                    }
            else:
                logger.error("健康日记列表获取失败: 空响应")
                return {
                    "status": "error",
                    "message": "健康日记列表获取失败: 空响应",
                    "data": []
                }
        except Exception as e:
            error_msg = f"健康日记列表获取异常: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.last_error = error_msg
            return {
                "status": "error",
                "message": error_msg,
                "data": []
            }

    def sync_record(self, table_name, record, extra_data=None):
        """同步记录到云端
        
        Args:
            table_name (str): 表名
            record (dict): 记录数据
            extra_data (dict, optional): 额外数据，如关联表数据
            
        Returns:
            bool: 同步成功返回True，失败返回False
        """
        if not self.is_authenticated():
            logger.error("未认证，无法同步记录")
            self.last_error = "未认证，无法同步记录"
            return False
            
        try:
            # 准备同步数据
            sync_data = {
                'table_name': table_name,
                'record': record
            }
            
            # 添加额外数据
            if extra_data:
                sync_data['extra_data'] = extra_data
            
            # 准备请求头
            headers = {}
            if self.token:
                headers['Authorization'] = f"Bearer {self.token}"
            if self.custom_id:
                headers['X-User-ID'] = self.custom_id
                
            logger.info(f"同步记录到云端: {table_name}, 记录ID: {record.get('id', 'N/A')}")
            
            # 发送同步请求
            result = self._make_request(
                method="POST",
                endpoint="sync/record",
                json_data=sync_data,
                headers=headers,
                max_retries=3
            )
            
            if result and (result.get("status") == "success" or result.get("success")):
                logger.info(f"记录同步成功: {table_name}/{record.get('id', 'N/A')}")
                return True
            else:
                error_msg = result.get("message", "同步失败，未知错误") if result else "同步请求失败"
                logger.error(f"记录同步失败: {error_msg}")
                self.last_error = error_msg
                return False
                
        except Exception as e:
            error_msg = f"同步记录时出错: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.last_error = error_msg
            return False

    def delete_record(self, table_name, record_id):
        """删除云端记录
        
        Args:
            table_name (str): 表名
            record_id (str): 记录ID
            
        Returns:
            bool: 删除成功返回True，失败返回False
        """
        if not self.is_authenticated():
            logger.error("未认证，无法删除记录")
            self.last_error = "未认证，无法删除记录"
            return False
            
        try:
            # 准备请求头
            headers = {}
            if self.token:
                headers['Authorization'] = f"Bearer {self.token}"
            if self.custom_id:
                headers['X-User-ID'] = self.custom_id
                
            logger.info(f"删除云端记录: {table_name}/{record_id}")
            
            # 发送删除请求 - 使用DELETE方法到sync/record端点
            delete_data = {
                'table_name': table_name,
                'record_id': record_id,
                'operation': 'delete'
            }
            
            result = self._make_request(
                method="DELETE",
                endpoint="sync/record",
                json_data=delete_data,
                headers=headers,
                max_retries=3
            )
            
            # 支持404(资源不存在)视为幂等成功
            if result and (result.get("status") == "success" or result.get("success") or result.get("not_found")):
                if result.get("not_found"):
                    logger.info(f"记录不存在(404)，视为已删除成功: {table_name}/{record_id}")
                else:
                    logger.info(f"记录删除成功: {table_name}/{record_id}")
                return True
            else:
                error_msg = result.get("message", "删除失败，未知错误") if result else "删除请求失败"
                logger.error(f"记录删除失败: {error_msg}")
                self.last_error = error_msg
                return False
                
        except Exception as e:
            error_msg = f"删除记录时出错: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.last_error = error_msg
            return False

    def get_assessment_reports(self, custom_id=None, status="completed", page=1, limit=20):
        """获取已完成的量表报告列表（从assessment_results表）
        
        Args:
            custom_id (str, optional): 用户ID，默认使用当前用户
            status (str, optional): 状态过滤，默认为completed
            page (int, optional): 页码，默认1
            limit (int, optional): 每页数量，默认20
            
        Returns:
            dict: 包含报告列表的响应
        """
        # 准备请求头
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        
        # 确定使用的custom_id
        user_id = custom_id or self.custom_id
        if user_id:
            headers['X-User-ID'] = user_id
            logger.info(f"添加X-User-ID头: {user_id}")
        
        # 准备查询参数
        params = {
            'skip': (page - 1) * limit,
            'limit': limit
        }
        
        logger.info(f"获取量表报告列表: custom_id={user_id}, status={status}")
        
        try:
            # 调用API获取报告列表 - 使用assessment_results端点
            endpoint = f"assessment_results/user/{user_id}" if user_id else "assessment_results"
            
            result = self._make_request(
                method="GET",
                endpoint=endpoint,
                params=params,
                headers=headers,
                max_retries=3
            )
            
            if result:
                # 处理后端返回的数据格式
                if isinstance(result, dict) and "data" in result:
                    data = result["data"]
                    total = result.get("total", len(data))
                    logger.info(f"成功获取量表报告列表，共 {total} 条")
                    
                    # 为每个项目添加item_type标记
                    for item in data:
                        item['item_type'] = 'assessment_report'
                    
                    return {
                        "status": "success",
                        "data": data,
                        "total": total
                    }
                elif isinstance(result, list):
                    logger.info(f"成功获取量表报告列表，共 {len(result)} 条")
                    # 为每个项目添加item_type标记
                    for item in result:
                        item['item_type'] = 'assessment_report'
                    return {
                        "status": "success",
                        "data": result,
                        "total": len(result)
                    }
                else:
                    logger.warning(f"未知的响应格式: {type(result)}, 内容: {result}")
                    return {
                        "status": "success",
                        "data": [],
                        "total": 0
                    }
            else:
                logger.error("获取量表报告列表失败: 空响应")
                return {
                    "status": "error",
                    "message": "获取量表报告列表失败: 空响应",
                    "data": []
                }
        except Exception as e:
            error_msg = f"用户注册异常: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.last_error = error_msg
            return {"status": "error", "message": error_msg}

    def get_questionnaire_reports(self, custom_id=None, status="completed", page=1, limit=20):
        """获取已完成的问卷报告列表（从questionnaire_results表）
        
        Args:
            custom_id (str, optional): 用户ID，默认使用当前用户
            status (str, optional): 状态过滤，默认为completed
            page (int, optional): 页码，默认1
            limit (int, optional): 每页数量，默认20
            
        Returns:
            dict: 包含报告列表的响应
        """
        # 准备请求头
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        
        # 确定使用的custom_id
        user_id = custom_id or self.custom_id
        if user_id:
            headers['X-User-ID'] = user_id
            logger.info(f"添加X-User-ID头: {user_id}")
        
        # 准备查询参数
        params = {
            'skip': (page - 1) * limit,
            'limit': limit
        }
        
        logger.info(f"获取问卷报告列表: custom_id={user_id}, status={status}")
        
        try:
            # 调用API获取报告列表 - 使用questionnaire_results端点
            endpoint = f"questionnaire_results/user/{user_id}" if user_id else "questionnaire_results"
            
            result = self._make_request(
                method="GET",
                endpoint=endpoint,
                params=params,
                headers=headers,
                max_retries=3
            )
            
            if result:
                # 处理后端返回的数据格式
                if isinstance(result, dict) and "data" in result:
                    data = result["data"]
                    total = result.get("total", len(data))
                    logger.info(f"成功获取问卷报告列表，共 {total} 条")
                    
                    # 为每个项目添加item_type标记
                    for item in data:
                        item['item_type'] = 'questionnaire_report'
                    
                    return {
                        "status": "success",
                        "data": data,
                        "total": total
                    }
                elif isinstance(result, list):
                    logger.info(f"成功获取问卷报告列表，共 {len(result)} 条")
                    # 为每个项目添加item_type标记
                    for item in result:
                        item['item_type'] = 'questionnaire_report'
                    return {
                        "status": "success",
                        "data": result,
                        "total": len(result)
                    }
                else:
                    logger.warning(f"未知的响应格式: {type(result)}, 内容: {result}")
                    return {
                        "status": "success",
                        "data": [],
                        "total": 0
                    }
            else:
                logger.error("获取问卷报告列表失败: 空响应")
                return {
                    "status": "error",
                    "message": "获取问卷报告列表失败: 空响应",
                    "data": []
                }
        except Exception as e:
            error_msg = f"获取问卷报告列表异常: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.last_error = error_msg
            return {
                "status": "error",
                "message": error_msg,
                "data": []
            }
    
    def get_report_detail(self, report_id, report_type="assessment"):
        """获取报告详情
        
        Args:
            report_id (int): 报告ID
            report_type (str): 报告类型，assessment或questionnaire
            
        Returns:
            dict: 报告详情
        """
        # 准备请求头
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if self.custom_id:
            headers['X-User-ID'] = self.custom_id
            logger.info(f"添加X-User-ID头: {self.custom_id}")
        
        logger.info(f"获取{report_type}报告详情，ID: {report_id}")
        
        try:
            # 根据报告类型确定正确的端点
            if report_type == "assessment" or report_type == "assessment_report":
                endpoint = f"assessment_results/{report_id}"
            else:
                endpoint = f"questionnaire_results/{report_id}"
        
            logger.info(f"使用端点: {endpoint}")
            
            # 发送请求
            result = self._make_request(
                method="GET",
                endpoint=endpoint,
                headers=headers,
                max_retries=3,
                timeout=self.timeout
            )
            
            if result:
                logger.info(f"成功获取报告详情: {report_id}")
                
                # 处理后端返回的标准格式 {"success": True, "data": {...}}
                if isinstance(result, dict):
                    if "success" in result and "data" in result:
                        # 标准后端响应格式
                        data = result["data"]
                    else:
                        # 直接数据格式
                        data = result
                    
                    # 确保有基本字段
                    if isinstance(data, dict):
                        if 'title' not in data:
                            if 'result_category' in data:
                                data['title'] = data['result_category']
                            else:
                                data['title'] = '报告详情'
                        
                        # 确保有report字段
                        if 'report' not in data and 'report_content' in data:
                            data['report'] = data['report_content']
                    
                    return {
                        "status": "success",
                        "data": data
                    }
                else:
                    logger.warning(f"报告详情响应格式异常: {type(result)}")
                    return {
                        "status": "success",
                        "data": {"report": str(result), "title": "报告详情"}
                    }
            else:
                # 根据报告类型获取相应的报告列表
                if report_type == "assessment":
                    reports_result = self.get_assessment_reports(custom_id=self.custom_id)
                else:
                    reports_result = self.get_questionnaire_reports(custom_id=self.custom_id)
                
                if reports_result and reports_result.get("status") == "success":
                    data = reports_result.get("data", [])
                    
                    # 处理不同的数据格式
                    if isinstance(data, dict) and "data" in data:
                        data = data["data"]
                    
                    if isinstance(data, list):
                        # 在列表中查找匹配的报告
                        for report in data:
                            if str(report.get("id")) == str(report_id):
                                logger.info(f"从报告列表中找到匹配的报告: {report_id}")
                                return {
                                    "status": "success",
                                    "data": report
                                }
                
                logger.error(f"获取报告详情失败: {report_id}")
                return {
                    "status": "error",
                    "message": "获取报告详情失败: 未找到报告"
                }
                
        except Exception as e:
            error_msg = f"获取报告详情异常: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.last_error = error_msg
            return {
                "status": "error",
                "message": error_msg
            }
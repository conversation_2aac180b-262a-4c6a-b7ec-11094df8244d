"""
Global BaseScreen for consistent UI structure across all screens.
- Fixed layout: Top header_container + ScrollView(content_container)
- Inherit and add content to self.ids['content_container']
- Compatible with KivyMD 2.0.1, theme.py fallbacks
- Public API: setup_top_app_bar, setup_logo, etc.
"""

import logging
from typing import List, Dict, Any, Optional
from kivy.uix.screenmanager import Screen
from kivy.lang import Builder
from kivy.metrics import dp
from kivy.properties import BooleanProperty, StringProperty, ObjectProperty, ListProperty
from kivy.graphics import Color, Rectangle
from kivy.clock import Clock
# 添加functools模块用于解决闭包陷阱
import functools

from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDIconButton
from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText

# Theme fallbacks
try:
    from mobile.theme import AppTheme as ThemeAppTheme, AppMetrics as ThemeAppMetrics
    # Create module-level variables to store theme values
    _PRIMARY_COLOR = getattr(ThemeAppTheme, "PRIMARY_COLOR", [0.133, 0.46, 0.82, 1])
    _TEXT_SECONDARY = getattr(ThemeAppTheme, "TEXT_SECONDARY", [0.45, 0.45, 0.45, 1])
    _PRIMARY_LIGHT = getattr(ThemeAppTheme, "PRIMARY_LIGHT", [0.95, 0.97, 1, 1])
    _NAVBAR_HEIGHT = getattr(ThemeAppMetrics, "NAVBAR_HEIGHT", 56)
except ImportError:
    _PRIMARY_COLOR = [0.133, 0.46, 0.82, 1]
    _TEXT_SECONDARY = [0.45, 0.45, 0.45, 1]
    _PRIMARY_LIGHT = [0.95, 0.97, 1, 1]
    _NAVBAR_HEIGHT = 56

# Create simple classes to hold the values
class AppTheme:
    PRIMARY_COLOR = _PRIMARY_COLOR
    TEXT_SECONDARY = _TEXT_SECONDARY
    PRIMARY_LIGHT = _PRIMARY_LIGHT

class AppMetrics:
    NAVBAR_HEIGHT = _NAVBAR_HEIGHT

# Widgets fallbacks
try:
    from mobile.widgets.logo import HealthLogo
except ImportError:
    from kivy.uix.widget import Widget
    class HealthLogo(Widget):
        pass

# Try to import GlobalTopAppBar, fallback to a simple class
_GlobalTopAppBarClass = None
try:
    from mobile.widgets.top_app_bar import GlobalTopAppBar
    _GlobalTopAppBarClass = GlobalTopAppBar
except ImportError:
    # Last resort fallback - create a minimal class
    class _MinimalGlobalTopAppBar:
        def __init__(self, **kwargs):
            # Set attributes that might be accessed
            self.parent = None
            self.title = ""
            self.right_action_items = []
    _GlobalTopAppBarClass = _MinimalGlobalTopAppBar

# Logo manager fallbacks
# Logo管理器已被移除，Logo现在由BaseScreen统一管理
pass

# KV for BaseScreen - Fixed global structure
BASE_SCREEN_KV = """
<BaseScreen>:
    MDBoxLayout:
        orientation: 'vertical'

        # Top header container (app bar + logo)
        MDBoxLayout:
            id: header_container
            orientation: 'vertical'
            size_hint_y: None
            height: max(self.minimum_height, dp(56))  # 确保至少有56dp高度以显示顶端导航栏
            padding: dp(0)

        # Content: ScrollView with container
        ScrollView:
            id: content_scroll
            bar_width: dp(10)
            scroll_type: ['content']
            do_scroll_x: False
            do_scroll_y: True

            MDBoxLayout:
                id: content_container
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(16), dp(16), dp(16)]
                spacing: dp(16)
"""

Builder.load_string(BASE_SCREEN_KV)

class BaseScreen(Screen):
    """
    Base class for all screens: Ensures consistent UI structure.
    Subclasses: Inherit, add widgets to self.ids['content_container'] in init_ui or on_pre_enter.
    """
    is_initialized = BooleanProperty(False)
    screen_title = StringProperty("标题")
    show_top_bar = BooleanProperty(True)
    top_bar_action_icon = StringProperty("refresh")
    top_app_bar = ObjectProperty(None)
    skip_bottom_nav = BooleanProperty(False)  # For login etc.
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.logger = logging.getLogger(self.__class__.__name__)
        self._logo_added = False
        # 不在初始化时设置背景色，而是在on_pre_enter中设置
        self._bg_initialized = False
        
        # 初始化top_app_bar为None
        self.top_app_bar = None
        
        # 使用Clock.schedule_once确保在下一帧更新背景色
        Clock.schedule_once(self._delayed_setup_background, 0)
        
    def _delayed_setup_background(self, dt):
        """延迟设置背景色，确保在屏幕完全初始化后设置"""
        self.setup_background()
        self._update_rect(None, None)

    def on_pre_enter(self, *args):
        """Global init hook: Ensures structure on enter."""
        # 每次进入屏幕时都更新背景色
        self.setup_background()
        self._update_rect(None, None)
        
        # 重置Logo添加标志，确保每次进入都能正确检查
        if not self.is_initialized:
            self.init_ui()
        else:
            # 即使已初始化，也要重新检查Logo状态
            self._logo_added = self.has_logo()
    
    def init_ui(self, dt=0):
        """Initialize UI components."""
        try:
            self.logger.info(f"[BaseScreen] 初始化UI: {self.__class__.__name__}")

            # Setup header
            if self.show_top_bar:
                self.setup_top_app_bar()
            else:
                self.logger.info("[BaseScreen] 跳过顶端导航栏设置")

            # Hook for subclass content setup
            self.do_content_setup()

            self.is_initialized = True
            self.logger.info("[BaseScreen] UI初始化完成")
        except (AttributeError, ValueError) as e:
            self.logger.error(f"init_ui error: {e}")

    def do_content_setup(self):
        """Override in subclasses to add custom content to content_container."""
        pass

    def setup_top_app_bar(self):
        """Setup top app bar in header_container."""
        if self.top_app_bar:
            self.logger.info("[BaseScreen] 顶端导航栏已存在")
            return
        try:
            # Try project-specific GlobalTopAppBar first
            if _GlobalTopAppBarClass:
                self.logger.info(f"[BaseScreen] 创建顶端导航栏: {self.screen_title}")
                self.top_app_bar = _GlobalTopAppBarClass(
                    title=self.screen_title,
                    action_icon=self.top_bar_action_icon,
                    parent_screen=self  # 传递父屏幕引用
                )
                # 确保parent_screen属性被正确设置
                if hasattr(self.top_app_bar, 'parent_screen'):
                    self.top_app_bar.parent_screen = self
                    self.logger.info(f"[BaseScreen] 成功设置parent_screen: {self}")
                else:
                    self.logger.warning("[BaseScreen] GlobalTopAppBar没有parent_screen属性")
                
                self.ids['header_container'].add_widget(self.top_app_bar)
                self.logger.info("[BaseScreen] 成功添加顶端导航栏")
                # 强制更新header_container的高度
                self.ids['header_container'].height = max(self.ids['header_container'].height, dp(56))
                # 强制刷新布局
                self.ids['header_container'].do_layout()
            else:
                self.logger.warning("[BaseScreen] 未找到GlobalTopAppBar类")
        except (AttributeError, TypeError, Exception) as e:
            # Final fallback - no top app bar
            self.logger.error(f"[BaseScreen] 创建顶端导航栏失败: {e}")
            self.top_app_bar = None

    def setup_logo(self):
        """Add logo to header if not present.

        仅由BaseScreen负责在header_container中添加Logo，避免重复。
        """
        # 获取header容器
        try:
            header = self.ids.get('header_container')
        except Exception:
            header = None
        if not header:
            self.logger.warning("[BaseScreen] 缺少header_container，无法添加Logo")
            return

        # 更严格的检查是否已存在Logo
        existing_logo = None
        for child in getattr(header, 'children', []):
            if isinstance(child, HealthLogo) or (hasattr(child, '__class__') and child.__class__.__name__ == 'HealthLogo'):
                existing_logo = child
                break

        # 如果已存在Logo，确保它有标识属性并返回
        if existing_logo:
            # 添加标识属性以更容易识别
            if not hasattr(existing_logo, '_base_screen_logo'):
                setattr(existing_logo, '_base_screen_logo', True)
            self._logo_added = True
            self.logger.info("[BaseScreen] Logo已存在，跳过添加")
            return

        # 添加Logo
        try:
            self.logger.info(f"[BaseScreen] 为 {self.__class__.__name__} 添加Logo")
            logo = HealthLogo(size_hint_y=None, height=dp(120))
            # 添加标识属性
            setattr(logo, '_base_screen_logo', True)
            header.add_widget(logo)
            self._logo_added = True
            self.logger.info("[BaseScreen] Logo添加成功")
        except Exception as e:
            self.logger.error(f"添加Logo失败: {e}")

    def has_logo(self) -> bool:
        """Check if logo exists in header."""
        try:
            header = self.ids.get('header_container')
            if not header:
                return False
                
            # 更严格的检查
            for child in getattr(header, 'children', []):
                if isinstance(child, HealthLogo) or (hasattr(child, '__class__') and child.__class__.__name__ == 'HealthLogo'):
                    # 添加标识属性以更容易识别
                    if not hasattr(child, '_base_screen_logo'):
                        setattr(child, '_base_screen_logo', True)
                    return True
            return False
        except Exception:
            return False

    def is_current_screen(self) -> bool:
        """检查当前屏幕是否是活动屏幕"""
        try:
            from kivy.app import App
            app = App.get_running_app()
            if app and hasattr(app, 'root') and hasattr(app.root, 'current_screen'):
                return app.root.current_screen == self
            return False
        except Exception:
            return False

    def setup_background(self):
        """Setup background color."""
        # 检查是否已经初始化背景色
        if getattr(self, '_bg_initialized', False):
            return
            
        try:
            if hasattr(self, 'canvas') and self.canvas and hasattr(self.canvas, 'before'):
                with self.canvas.before:
                    # 获取应用实例
                    app = MDApp.get_running_app()
                    if app and hasattr(app, 'theme') and hasattr(app.theme, 'PRIMARY_LIGHT'):
                        bg_color = app.theme.PRIMARY_LIGHT
                    else:
                        # 回退到默认颜色 #E3F2FD (淡蓝色)
                        bg_color = [0.89, 0.95, 1.0, 1]
                    Color(*bg_color)
                    self.rect = Rectangle(size=self.size, pos=self.pos)
                    # 标记背景色已初始化
                    self._bg_initialized = True
                    
                # 使用getattr获取bind方法，避免类型检查错误
                bind_method = getattr(self, 'bind', None)
                if bind_method:
                    bind_method(size=self._update_rect, pos=self._update_rect)
        except Exception as e:
            self.logger.error(f"设置背景色失败: {e}")
            import traceback
            traceback.print_exc()

    def _update_rect(self, instance, value):
        try:
            if hasattr(self, 'rect'):
                self.rect.pos = self.pos
                self.rect.size = self.size
        except Exception as e:
            self.logger.error(f"更新背景矩形失败: {e}")

    def on_enter(self, *args):
        """Called when screen is entered."""
        # 调用父类方法确保基础结构已初始化
        super().on_enter(*args)

        # 处理顶端导航栏
        if self.show_top_bar and not self.top_app_bar:
            self.setup_top_app_bar()

        # 确保Logo存在，但避免重复添加
        try:
            if not self.has_logo():
                self.logger.info("[BaseScreen] 当前屏幕缺少Logo，添加Logo")
                self.setup_logo()
            else:
                self.logger.info("[BaseScreen] Logo已存在，跳过添加")
        except Exception as e:
            self.logger.warning(f"[BaseScreen] Logo检查或添加时异常: {e}")

    def on_action(self):
        """Handle top bar action (e.g., refresh)."""
        if hasattr(self, 'refresh_data'):
            try:
                self.refresh_data()
            except Exception:
                pass

    def refresh_data(self):
        """Override for data refresh."""
        pass

    def on_back(self):
        """Default back navigation."""
        try:
            self.logger.info("[BaseScreen] 执行返回操作")
            app = MDApp.get_running_app()
            
            # 获取屏幕管理器实例
            screen_manager = None
            if app and hasattr(app, 'screen_manager') and app.screen_manager:
                screen_manager = app.screen_manager
                self.logger.info("[BaseScreen] 从app获取到screen_manager")
            elif app and hasattr(app, 'root') and app.root:
                # 如果root是主布局，尝试从其子组件中找到屏幕管理器
                for child in app.root.children:
                    if hasattr(child, 'go_back'):
                        screen_manager = child
                        self.logger.info("[BaseScreen] 从root的子组件中找到screen_manager")
                        break
            
            if screen_manager and hasattr(screen_manager, 'go_back'):
                try:
                    self.logger.info("[BaseScreen] 尝试调用screen_manager.go_back()")
                    success = screen_manager.go_back()
                    if success:
                        self.logger.info("[BaseScreen] 成功调用screen_manager.go_back()")
                        return
                    else:
                        self.logger.info("[BaseScreen] screen_manager.go_back()返回False")
                except Exception as e:
                    self.logger.error(f"[BaseScreen] 调用screen_manager.go_back()失败: {e}")
                    import traceback
                    self.logger.error(traceback.format_exc())
            else:
                self.logger.warning("[BaseScreen] 未找到有效的screen_manager")
            
            # 如果go_back失败，直接切换到主页
            if screen_manager:
                self.logger.info("[BaseScreen] 直接切换到主页")
                try:
                    screen_manager.current = 'homepage_screen'
                    self.logger.info("[BaseScreen] 成功切换到主页")
                    return
                except Exception as e:
                    self.logger.error(f"[BaseScreen] 切换到主页失败: {e}")
                    import traceback
                    self.logger.error(traceback.format_exc())
            
            self.logger.warning("[BaseScreen] 无法执行返回操作")
        except Exception as e:
            self.logger.error(f"[BaseScreen] 返回操作失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    def hide_top_bar(self):
        """Hide top bar."""
        self.show_top_bar = False
        if self.top_app_bar and hasattr(self.top_app_bar, 'parent') and self.top_app_bar.parent:
            try:
                self.top_app_bar.parent.remove_widget(self.top_app_bar)
            except Exception:
                pass

    def show_top_bar_widget(self):
        """Show top bar."""
        self.show_top_bar = True
        if not getattr(self, 'top_app_bar', None):
            self.setup_top_app_bar()

    def show_snackbar(self, message, message_type="info"):
        """显示Snackbar消息
        
        Args:
            message (str): 要显示的消息
            message_type (str): 消息类型，可选值: "info", "error", "success", "warning"
        """
        try:
            # 根据消息类型设置颜色
            color_map = {
                "error": [0.96, 0.26, 0.21, 1],      # 红色
                "success": [0.30, 0.69, 0.31, 1],    # 绿色
                "warning": [1.0, 0.76, 0.03, 1],     # 橙色
                "info": [0.133, 0.46, 0.82, 1]       # 蓝色（主题色）
            }
            
            bg_color = color_map.get(message_type, color_map["info"])
            
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                md_bg_color=bg_color,
                duration=3,
                pos_hint={"center_x": 0.5}
            )
            snackbar.open()
            self.logger.info(f"显示Snackbar消息: {message} (类型: {message_type})")
        except Exception as e:
            self.logger.error(f"显示Snackbar失败: {e}")
            # 如果Snackbar失败，至少在日志中记录消息
            self.logger.info(f"消息: {message}")
"""
密码工具模块
提供密码哈希和验证功能，与后端保持一致
"""

import hashlib
import base64
import logging

logger = logging.getLogger(__name__)

def simple_hash(password: str) -> str:
    """
    简单的哈希函数，与后端保持一致
    
    Args:
        password: 明文密码
        
    Returns:
        str: 哈希后的密码
    """
    salt = "21db1a7f-63b2-4608-9c89-b76fe5666f27"  # 与后端相同的固定盐值
    salted = (password + salt).encode('utf-8')
    hashed = hashlib.sha256(salted).digest()
    return base64.b64encode(hashed).decode('utf-8')

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码
    
    Args:
        plain_password: 明文密码
        hashed_password: 哈希后的密码
        
    Returns:
        bool: 验证结果
    """
    import logging
    logger = logging.getLogger(__name__)
    
    # 记录验证过程
    logger.info(f"移动端登录 - 找到用户: markey, 密码哈希: {hashed_password[:20]}...")
    
    # 1. 优先尝试使用passlib验证（与后端保持一致）
    try:
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["sha256_crypt", "bcrypt"], deprecated="auto")
        
        if pwd_context.verify(plain_password, hashed_password):
            logger.info("使用passlib(sha256_crypt)验证密码成功")
            return True
        else:
            logger.warning("passlib验证失败")
    except ImportError:
        logger.warning("passlib模块未安装，跳过passlib验证")
    except Exception as e:
        logger.warning(f"passlib验证出错: {str(e)}")

    # 2. 尝试使用bcrypt验证（如果哈希以$2b$或$2a$开头）
    if hashed_password.startswith("$2"):
        try:
            import bcrypt
            
            # 将明文密码转换为字节
            password_bytes = plain_password.encode('utf-8')
            hashed_bytes = hashed_password.encode('utf-8')
            
            # 使用bcrypt验证
            if bcrypt.checkpw(password_bytes, hashed_bytes):
                logger.info("使用bcrypt验证密码成功")
                return True
            else:
                logger.warning("bcrypt验证失败")
        except ImportError:
            logger.warning("bcrypt模块未安装，跳过bcrypt验证")
        except Exception as e:
            logger.warning(f"bcrypt验证出错: {str(e)}")

    # 3. 尝试使用简单哈希比较
    try:
        simple_hashed = simple_hash(plain_password)
        if simple_hashed == hashed_password:
            logger.info("使用简单哈希验证密码成功")
            return True
        else:
            logger.warning("简单哈希验证失败")
    except Exception as e:
        logger.warning(f"简单哈希验证出错: {str(e)}")

    # 4. 尝试直接比较（用于明文密码的情况）
    try:
        if plain_password == hashed_password:
            logger.info("直接比较验证密码成功（明文密码）")
            return True
    except Exception as e:
        logger.warning(f"直接比较出错: {str(e)}")

    # 所有验证方法都失败
    logger.warning("移动端登录 - 密码验证结果: False")
    logger.warning("所有密码验证方法都失败")
    return False

def get_password_hash(password: str) -> str:
    """
    获取密码哈希，与后端保持一致
    
    Args:
        password: 明文密码
        
    Returns:
        str: 哈希后的密码
    """
    try:
        # 尝试使用passlib生成哈希
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["sha256_crypt", "bcrypt"], deprecated="auto")
        
        hashed = pwd_context.hash(password)
        logger.info("成功使用sha256_crypt生成密码哈希")
        return hashed
    except ImportError:
        logger.warning("passlib模块未安装，使用简单哈希")
    except Exception as e:
        logger.error(f"密码哈希出错，使用简单哈希: {str(e)}")

    # 尝试使用bcrypt
    try:
        import bcrypt
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
        logger.info("成功使用bcrypt生成密码哈希")
        return hashed
    except ImportError:
        logger.warning("bcrypt模块未安装，使用简单哈希")
    except Exception as e:
        logger.error(f"bcrypt哈希出错: {str(e)}")

    # 如果都失败，使用简单哈希
    simple_hashed = simple_hash(password)
    logger.warning("使用简单哈希生成密码哈希（不安全）")
    return simple_hashed